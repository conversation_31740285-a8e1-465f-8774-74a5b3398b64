import { Badge } from "@/components/ui/badge";
import { ServiceStatus } from "@/types/services";
import { cn } from "@/lib/utils";

interface ServiceStatusBadgeProps {
  status: ServiceStatus;
  className?: string;
}

export function ServiceStatusBadge({ status, className }: ServiceStatusBadgeProps) {
  const getStatusConfig = (status: ServiceStatus) => {
    switch (status) {
      case 'requested':
        return {
          label: 'Solicitată',
          variant: 'secondary' as const,
          className: 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800',
          icon: '📝'
        };
      case 'scheduled':
        return {
          label: 'Programată',
          variant: 'default' as const,
          className: 'bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-950 dark:text-purple-300 dark:border-purple-800',
          icon: '📅'
        };
      case 'inProgress':
        return {
          label: 'În lucru',
          variant: 'default' as const,
          className: 'bg-orange-50 text-orange-700 border-orange-200 dark:bg-orange-950 dark:text-orange-300 dark:border-orange-800',
          icon: '🔧'
        };
      case 'diagnosisComplete':
        return {
          label: 'Diagnoză completă',
          variant: 'default' as const,
          className: 'bg-indigo-50 text-indigo-700 border-indigo-200 dark:bg-indigo-950 dark:text-indigo-300 dark:border-indigo-800',
          icon: '🔍'
        };
      case 'awaitingParts':
        return {
          label: 'Așteptare piese',
          variant: 'secondary' as const,
          className: 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300 dark:border-yellow-800',
          icon: '📦'
        };
      case 'awaitingApproval':
        return {
          label: 'Așteptare aprobare',
          variant: 'default' as const,
          className: 'bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-950 dark:text-amber-300 dark:border-amber-800',
          icon: '⏳'
        };
      case 'completed':
        return {
          label: 'Finalizat',
          variant: 'default' as const,
          className: 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800',
          icon: '✅'
        };
      case 'cancelled':
        return {
          label: 'Anulat',
          variant: 'destructive' as const,
          className: 'bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300 dark:border-red-800',
          icon: '❌'
        };
      case 'delivered':
        return {
          label: 'Livrat',
          variant: 'default' as const,
          className: 'bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-950 dark:text-emerald-300 dark:border-emerald-800',
          icon: '🚚'
        };
      default:
        return {
          label: status,
          variant: 'outline' as const,
          className: 'bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-950 dark:text-gray-300 dark:border-gray-800',
          icon: '❓'
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <Badge 
      variant={config.variant}
      className={cn(
        'inline-flex items-center gap-1.5 px-2.5 py-1 text-xs font-medium rounded-full border',
        config.className,
        className
      )}
    >
      <span className="text-xs" role="img" aria-hidden="true">
        {config.icon}
      </span>
      {config.label}
    </Badge>
  );
}

// Service status display utility function
export function getServiceStatusDisplay(status: ServiceStatus) {
  switch (status) {
    case 'requested':
      return {
        label: 'Solicitată',
        description: 'Cererea de service a fost înregistrată',
        color: 'blue',
        icon: '📝'
      };
    case 'scheduled':
      return {
        label: 'Programată',
        description: 'Serviciul a fost programat',
        color: 'purple',
        icon: '📅'
      };
    case 'inProgress':
      return {
        label: 'În lucru',
        description: 'Serviciul este în desfășurare',
        color: 'orange',
        icon: '🔧'
      };
    case 'diagnosisComplete':
      return {
        label: 'Diagnoză completă',
        description: 'Diagnoza a fost finalizată',
        color: 'indigo',
        icon: '🔍'
      };
    case 'awaitingParts':
      return {
        label: 'Așteptare piese',
        description: 'Se așteaptă livrarea pieselor necesare',
        color: 'yellow',
        icon: '📦'
      };
    case 'awaitingApproval':
      return {
        label: 'Așteptare aprobare',
        description: 'Se așteaptă aprobarea clientului',
        color: 'amber',
        icon: '⏳'
      };
    case 'completed':
      return {
        label: 'Finalizat',
        description: 'Serviciul a fost finalizat',
        color: 'green',
        icon: '✅'
      };
    case 'cancelled':
      return {
        label: 'Anulat',
        description: 'Serviciul a fost anulat',
        color: 'red',
        icon: '❌'
      };
    case 'delivered':
      return {
        label: 'Livrat',
        description: 'Vehiculul a fost livrat clientului',
        color: 'emerald',
        icon: '🚚'
      };
    default:
      return {
        label: status,
        description: 'Status necunoscut',
        color: 'gray',
        icon: '❓'
      };
  }
}

// Service status progress calculation
export function getServiceStatusProgress(status: ServiceStatus): number {
  switch (status) {
    case 'requested':
      return 10;
    case 'scheduled':
      return 25;
    case 'inProgress':
      return 40;
    case 'diagnosisComplete':
      return 60;
    case 'awaitingParts':
      return 70;
    case 'awaitingApproval':
      return 80;
    case 'completed':
      return 95;
    case 'delivered':
      return 100;
    case 'cancelled':
      return 0;
    default:
      return 0;
  }
}

// Check if status is final (no more changes expected)
export function isServiceStatusFinal(status: ServiceStatus): boolean {
  const finalStatuses: ServiceStatus[] = [
    'completed',
    'delivered',
    'cancelled'
  ];
  return finalStatuses.includes(status);
}

// Check if status allows cancellation
export function canCancelServiceRequest(status: ServiceStatus): boolean {
  const nonCancellableStatuses: ServiceStatus[] = [
    'completed',
    'delivered',
    'cancelled'
  ];
  return !nonCancellableStatuses.includes(status);
}
