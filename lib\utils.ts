"server-only"

import { Decimal } from "@/generated/prisma/runtime/library";
import { Return, ReturnStatus, ReturnTimelineEvent } from "@/types/returns";
// Service-related imports removed - now handled in ServiceStatusBadge component
import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function getClientIp(req: Request): string | null {
  const forwarded = req.headers.get('x-forwarded-for');
  if (forwarded) return forwarded.split(',')[0].trim();
  return req.headers.get('x-real-ip');
}

export  function getStockStatus(stock?: number) {
      if (stock === undefined) return "UNKNOWN";
      if (stock > 5) return "in stoc";
      if (stock > 0) return "stoc critic";
      return "furnizor extern";
}

interface StockStatus {
  statusText: string;
  dotColorClass: string;
}

//function for formating the data, RO
export function formatDate(dateString: string, options?: Intl.DateTimeFormatOptions): string {
  const defaultOptions: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  };
  
  return new Date(dateString).toLocaleDateString("ro-RO", options || defaultOptions);
}

export function getStockStatusData(stock: number | null | undefined): StockStatus {
  // Handle cases where stock might be null or undefined
  if (stock === null || stock === undefined || stock <= 0) {
    return { statusText: "furnizor extern", dotColorClass: "bg-red-500" };
  } else if (stock > 0 && stock <= 10) { // Example: 1 to 10 units
    return { statusText: "stoc critic", dotColorClass: "bg-orange-500" };
  } else { // Example: More than 10 units
    return { statusText: "in stoc", dotColorClass: "bg-green-500" };
  }
}

interface LocationDetails {
  name: string;
  address: string;
  phone?: string;
  nota?: string;
}

// A map from your location codes to their display names and addresses
export const locationDetailsMap: Record<string, LocationDetails> = {
  'CJ': { name: 'Baneasa', address: 'Str. George Bacovia 1, Bucuresti, Romania', phone: "+40751234567", nota: "Program de lucru: Luni - Vineri, 09:00 - 17:00", },
  'BV': { name: 'Otopeni', address: 'Calea Bucureștilor 224, Otopeni, Romania', phone: "+40751234567", nota: "Program de lucru: Luni - Vineri, 09:00 - 17:00", },
  'TM': { name: 'Cluj', address: 'Str. Fabricii 25, Cluj-Napoca, Romania', phone: "+40751234567", nota: "Program de lucru: Luni - Vineri, 09:00 - 17:00", },
  'AR': { name: 'Militari', address: 'Bd. Iuliu Maniu 7, Bucuresti, Romania', phone: "+40751234567", nota: "Program de lucru: Luni - Vineri, 09:00 - 17:00", },
  'BAC': { name: 'Constanta', address: 'Str. Zizinului 3, Brasov, Romania', phone: "+40751234567", nota: "Program de lucru: Luni - Vineri, 09:00 - 17:00", },
  'BAN': { name: 'Bacau', address: 'Str. Ardealului 10, Alba Iulia, Romania', phone: "+40751234567", nota: "Program de lucru: Luni - Vineri, 09:00 - 17:00", },
  'OTP': { name: 'Sibiu', address: 'Bd. Tomis 500, Constanta, Romania', phone: "+40751234567", nota: "Program de lucru: Luni - Vineri, 09:00 - 17:00", },
  'MIL': { name: 'Arad', address: 'Str. Republicii 12, Bacau, Romania', phone: "+40751234567", nota: "Program de lucru: Luni - Vineri, 09:00 - 17:00", },
  'TGM': { name: 'Timisoara', address: 'Calea Nationala 8, Botosani, Romania', phone: "+40751234567", nota: "Program de lucru: Luni - Vineri, 09:00 - 17:00", },
  'JIL': { name: 'Brasov', address: 'Calea Severinului 100, Craiova, Romania', phone: "+40751234567", nota: "Program de lucru: Luni - Vineri, 09:00 - 17:00", },
  'CT': { name: 'Craiova', address: 'Bd. Carol I 20, Craiova, Romania', phone: "+40751234567", nota: "Program de lucru: Luni - Vineri, 09:00 - 17:00", },
  'CRA': { name: 'Jilava', address: 'Str. Principală 150, Jilava, Romania', phone: "+40751234567", nota: "Program de lucru: Luni - Vineri, 09:00 - 17:00", },
  'SB': { name: 'Targu Mures', address: 'Str. Uzinei 1, Mioveni, Romania', phone: "+40751234567", nota: "Program de lucru: Luni - Vineri, 09:00 - 17:00", },
  // Fallback for any unexpected or unknown location codes
  'UNKNOWN': { name: 'Unknown Location', address: 'Address Not Available', phone: "+40751234567", nota: "Program de lucru: Luni - Vineri, 09:00 - 17:00", },
};

// Helper function to safely get location details
export function getLocationDetails(code: string): LocationDetails {
  return locationDetailsMap[code] || locationDetailsMap['UNKNOWN'];
}

export function formatPriceRON(price: number | null): string {
  if (typeof price !== "number") return "";

  const formatted = new Intl.NumberFormat("ro-RO", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(price);

  return `${formatted} Lei`; // Capitalized
}

export function formatDiscount(
  type: "PERCENTAGE" | "FIXED_AMOUNT" | "NEW_PRICE" | null,
  value: number | null
): string | null {
  if (type === null || value === null) return null;

  const formatted = value.toLocaleString("ro-RO", { minimumFractionDigits: 2 });

  switch (type) {
    case "PERCENTAGE":
      return `-${formatted}%`;
    case "FIXED_AMOUNT":
      return `-${formatted} Lei`;
    case "NEW_PRICE":
      return `${formatted} Lei`;
    default:
      return null;
  }
}

/**
 * Converts a value to a number safely, handling Prisma Decimal objects
 * @param x - The value to convert to a number
 * @returns A number, or null if conversion is not possible
 */
// export function toSafeNumber(x: unknown): number | null {
//   if (x == null) return null;
  
//   // Handle Prisma Decimal instance
//   if (isPrismaDecimal(x)) {
//     return x.toNumber();
//   }
  
//   // Handle string or number
//   const n = Number(x);
//   return Number.isNaN(n) ? null : n;
// }
export function toSafeNumber(x: unknown): number | null {
  if (x == null) return null;

  // Prisma Decimal
  if (isPrismaDecimal(x)) return x.toNumber();

  // Native number
  if (typeof x === 'number') return Number.isFinite(x) ? x : null;

  // Strings (incl. "1.234,56", "1,234.56", "  ", "")
  if (typeof x === 'string') {
    let s = x.trim();
    if (s === '') return null;

    // remove spaces, NBSP, thin spaces used as thousands
    s = s.replace(/[\s\u00A0\u202F]/g, '');

    const hasDot = s.includes('.');
    const hasComma = s.includes(',');

    if (hasDot && hasComma) {
      // Decide decimal by the last separator:
      if (s.lastIndexOf(',') > s.lastIndexOf('.')) {
        // European: 1.234,56 -> remove thousand dots, comma -> dot
        s = s.replace(/\.(?=\d{3}(?:\D|$))/g, '');
        s = s.replace(',', '.');
      } else {
        // US: 1,234.56 -> remove thousand commas
        s = s.replace(/,(?=\d{3}(?:\D|$))/g, '');
      }
    } else if (hasComma) {
      // Only commas: one comma => decimal, many => thousands
      const count = (s.match(/,/g) || []).length;
      s = count === 1 ? s.replace(',', '.') : s.replace(/,/g, '');
    } else if (hasDot) {
      // Only dots: if multiple, keep last as decimal
      const parts = s.split('.');
      if (parts.length > 2) {
        const dec = parts.pop()!;
        s = parts.join('') + '.' + dec;
      }
    }

    const n = Number(s);
    return Number.isFinite(n) ? n : null;
  }

  return null;
}


/**
 * Type guard to check if a value is a Prisma Decimal
 */
function isPrismaDecimal(value: unknown): value is Decimal {
  return (
    typeof value === 'object' && 
    value !== null && 
    'toNumber' in value && 
    typeof (value as Decimal).toNumber === 'function'
  );
}

export const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

const UNIT_TRANSLATIONS: Record<string,string> = {
  PCE:   "Bucata",
  PK:    "Pachet",
  MTR:   "Metru",
  PA:    "Set",
  LTR:   "Litru",
  GRM:   "Gram",
  CM:    "Centimetru",
  SQM:   "Metru Patrat",
  SHEET: "Foaie",
};

export function translateUnit(unit: string): string {
  return UNIT_TRANSLATIONS[unit] ?? unit;
}

  // Format phone number to E.164
export const formatPhoneNumber = (phone: string): string => {
    if (!phone) return '';
    
    // Remove all non-digit characters
    const digits = phone.replace(/\D/g, '');
    
    // If starts with 0 (Romanian format), replace with +40
    if (digits.startsWith('0')) {
      return `+40${digits.substring(1)}`;
    }
    
    // If doesn't start with +, assume Romanian and add +40
    if (!phone.startsWith('+')) {
      return `+40${digits}`;
    }
    
    return phone;
  };

// Validation functions
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 255;
}

export function isValidName(name: string | null): boolean {
  if (!name) return true; // Allow empty names
  return name.length <= 100 && !/[<>{}]/.test(name); // Basic XSS protection
}

export function isValidImageUrl(url: string | null): boolean {
  if (!url) return true; // Allow empty URLs
  try {
    const parsedUrl = new URL(url);
    return ['http:', 'https:'].includes(parsedUrl.protocol) && url.length <= 500;
  } catch {
    return false;
  }
}

export function maskEmail(email: string): string {
  const [local, domain] = email.split('@');
  if (!local || !domain) return email; // Fallback for invalid email format
  
  const maskedLocal = local.length > 2 ? 
    local.substring(0, 2) + '*'.repeat(local.length - 2) : 
    local;
  return `${maskedLocal}@${domain}`;
}

export function generateReturnTimeline(returnData: Return): ReturnTimelineEvent[] {
  const events: ReturnTimelineEvent[] = [];
  const currentStatus = returnData.status;

  // Define the standard return flow
  const statusFlow: { status: ReturnStatus; title: string; description: string }[] = [
    { status: 'requested', title: 'Returnare solicitată', description: 'Cererea de returnare a fost trimisă' },
    { status: 'approved', title: 'Returnare aprobată', description: 'Cererea de returnare a fost aprobată' },
    { status: 'awaitingReceipt', title: 'În așteptarea primirii', description: 'Așteptăm să primim produsele' },
    { status: 'received', title: 'Produse primite', description: 'Produsele au fost primite în depozit' },
    { status: 'inspected', title: 'Produse verificate', description: 'Produsele au fost verificate și evaluate' },
    { status: 'refundIssued', title: 'Rambursare procesată', description: 'Rambursarea a fost procesată' },
    { status: 'completed', title: 'Returnare finalizată', description: 'Procesul de returnare a fost finalizat' },
  ];

  // Handle rejected status separately
  if (currentStatus === 'rejected') {
    events.push({
      date: returnData.createdAt,
      status: 'requested',
      title: 'Returnare solicitată',
      description: 'Cererea de returnare a fost trimisă',
      isCompleted: true,
      isCurrent: false,
    });

    events.push({
      date: returnData.approvedAt || returnData.updatedAt,
      status: 'rejected',
      title: 'Returnare respinsă',
      description: returnData.rejectionReason || 'Cererea de returnare a fost respinsă',
      isCompleted: true,
      isCurrent: true,
    });

    return events;
  }

  // Handle cancelled status separately
  if (currentStatus === 'cancelled') {
    events.push({
      date: returnData.createdAt,
      status: 'requested',
      title: 'Returnare solicitată',
      description: 'Cererea de returnare a fost trimisă',
      isCompleted: true,
      isCurrent: false,
    });

    events.push({
      date: returnData.updatedAt,
      status: 'cancelled',
      title: 'Returnare anulată',
      description: 'Cererea de returnare a fost anulată',
      isCompleted: true,
      isCurrent: true,
    });

    return events;
  }

  // Generate events for normal flow
  let currentStatusReached = false;

  statusFlow.forEach(step => {
    let eventDate = returnData.createdAt;
    let isCompleted = false;
    let isCurrent = false;

    // Determine if this step is completed and get the appropriate date
    switch (step.status) {
      case 'requested':
        eventDate = returnData.createdAt;
        isCompleted = true;
        break;
      case 'approved':
        if (returnData.approvedAt) {
          eventDate = returnData.approvedAt;
          isCompleted = true;
        }
        break;
      case 'awaitingReceipt':
        if (['awaitingReceipt', 'received', 'inspected', 'refundIssued', 'completed'].includes(currentStatus)) {
          eventDate = returnData.approvedAt || returnData.updatedAt;
          isCompleted = true;
        }
        break;
      case 'received':
        if (returnData.receivedAt) {
          eventDate = returnData.receivedAt;
          isCompleted = true;
        }
        break;
      case 'inspected':
        if (returnData.inspectedAt) {
          eventDate = returnData.inspectedAt;
          isCompleted = true;
        }
        break;
      case 'refundIssued':
        if (returnData.refundedAt) {
          eventDate = returnData.refundedAt;
          isCompleted = true;
        }
        break;
      case 'completed':
        if (currentStatus === 'completed') {
          eventDate = returnData.updatedAt;
          isCompleted = true;
        }
        break;
    }

    // Check if this is the current step
    if (step.status === currentStatus) {
      isCurrent = true;
      currentStatusReached = true;
    }

    // Only add future steps if they haven't been reached yet
    if (!currentStatusReached || isCompleted || isCurrent) {
      events.push({
        date: eventDate,
        status: step.status,
        title: step.title,
        description: step.description,
        isCompleted,
        isCurrent,
      });
    }
  });

  return events;
}

// Service-related utility functions moved to ServiceStatusBadge component

// Service type functions removed - no longer needed with new schema

// Service duration formatting removed - not needed in new implementation

// All service utility functions moved to ServiceStatusBadge component
