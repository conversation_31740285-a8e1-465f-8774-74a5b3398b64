"use server"

import { getCurrentDbUser } from "@/lib/auth";
import { prisma, withRetry } from "@/lib/db";
import { logger } from "@/lib/logger";
import { getPriceFor4th } from "@/lib/mssql/query";
import { redis } from "@/lib/redis";
import { toSafeNumber } from "@/lib/utils";
import { addObservationsToCartSchema, cuidSchema, productCodSchema, updateCartSchema } from "@/lib/zod";
import { Cart } from "@/types/cart";
import { revalidatePath } from "next/cache";

export async function addItemToCart(product: string) {
  console.time('addItemToCart');
  try {

    const user = await getCurrentDbUser();
    if (!user) {
        logger.error(`[addItemToCart] No user authenticated`);
        return { success: false };
    }

    const parsed = productCodSchema.safeParse(product);

    if (!parsed.success) {
      logger.error("[addItemToCart] Invalid product ID provided:", parsed.error.format());
      return { success: false };
    }

    const validatedProduct = parsed.data;

    if (!redis) {
      logger.error("[addItemToCart] Redis connection not available");
      return { success: false};
    }

    // Retrieve the cart from Redis
    let cart: Cart | null = await redis.get(`cart-${user.id}`);

    // Fetch product details
    const selectedProduct = await withRetry(() =>
      prisma.product.findUnique({
        select: {
          id: true,
          Material_Number: true,
          Description_Local: true,
          ImageUrl: true,
          FinalPrice: true,
          Net_Weight: true,
        },
        where: { Material_Number: validatedProduct },
    }));

    if (!selectedProduct) {
      logger.error(`[addItemToCart] Product not found: ${validatedProduct}`);
      return { success: false};
    }

    let priceToUse = toSafeNumber(selectedProduct.FinalPrice) ?? 0;

    // 3️⃣ override if they’re in a 4th-level role
    const is4th =
      user.role.includes("fourLvlAdminAB") ||
      user.role.includes("fourLvlInregistratAB");
    if (is4th) {
      const userAM = user.userAM || "";
      const material_number = selectedProduct.Material_Number;
      const special = await getPriceFor4th(material_number, userAM);
      if (special?.pret != null) {
        priceToUse = special.pret;
      }
    }

    //if priceToUse is 0 or null then return success false
    if (priceToUse === 0 || priceToUse === null) return { success: false };

    // Initialize the cart if it doesn't exist
    if (!cart) {
      cart = { items: [] };
    }

    // Check if the product is already in the cart
    const existingItem = cart.items.find((item) => item.Material_Number === validatedProduct);

    if (existingItem) {
      existingItem.quantity += 1;
    } else {
      cart.items.push({
        id: selectedProduct.id,
        Material_Number: selectedProduct.Material_Number,
        Net_Weight: toSafeNumber(selectedProduct.Net_Weight),
        ImageUrl: selectedProduct.ImageUrl[0],
        Description_Local: selectedProduct.Description_Local ?? "",
        FinalPrice: priceToUse, 
        quantity: 1,
        vinNotes: "",
        addVinNotesToInvoice: false,
        addToOrder: true,
      });
    }

    // Save the updated cart to Redis
    if (!redis) {
      logger.error("[addItemToCart] Redis connection not available");
      return { success: false };
    }

    await redis.set(`cart-${user.id}`, cart);

    // Revalidate paths as needed
    revalidatePath("/", "layout");
    logger.info(`[addItemToCart] Added item to cart: ${user.id} - ${validatedProduct}`);
console.timeEnd('addItemToCart');
    return { success: true };
  } catch (error) {
    console.timeEnd('addItemToCart error');
    logger.error(`[addItemToCart] Error adding item to cart: ${error}`);
    return { success: false };
  }
}

export async function updateCartActiondata(data: {
  itemId?: string;
  vinNotes?: string | null;
  addVinNotesToInvoice?: boolean;
  addToOrder?: boolean;
  quantity?: number;
  orderNotes?: string;
}) {

  try {
    const user = await getCurrentDbUser()

    if (!user) {
        logger.error(`[updateCartActiondata] No user authenticated`);
        return { success: false };
    }

    //validate all params with zod
    const validatedData = updateCartSchema.safeParse(data);

    if (!validatedData.success) {
      logger.error("[updateCartActiondata] Invalid data provided:", validatedData.error.format());
      return { success: false };
    }

    const { itemId, vinNotes, addVinNotesToInvoice, addToOrder, quantity, orderNotes } = data; //quantity is optional, if not provided, it will not be updated

    const validatedProductId = cuidSchema.parse(itemId);

    if (!redis) {
      logger.error("[updateCartActiondata] Redis connection not available");
      return { success: false };
    }

    // Fetch the existing cart for the user
    const cartData = await redis.get<Cart>(`cart-${user.id}`);
    const cart: Cart = cartData ? cartData : { items: [], order: { notes: "" } };

    // Find the item to update
    const existingItemIndex = cart.items.findIndex(item => item.id === validatedProductId);
    if (existingItemIndex >= 0) {
      // Update the existing item
      cart.items[existingItemIndex] = {
        ...cart.items[existingItemIndex],
        vinNotes: vinNotes ?? cart.items[existingItemIndex].vinNotes,
        addVinNotesToInvoice: addVinNotesToInvoice ?? cart.items[existingItemIndex].addVinNotesToInvoice,
        addToOrder: addToOrder ?? cart.items[existingItemIndex].addToOrder,
        quantity: quantity ?? cart.items[existingItemIndex].quantity, 
      };
      //if quantity is equal to 0 then remove the item from redis
      if (quantity === 0) cart.items.splice(existingItemIndex, 1); //remove the item from redis if quantity is 0
    }

    if (orderNotes) {
      cart.order = {
        ...cart.order,
        notes: orderNotes,
      };
    }

    // Save the updated cart to Redis
    await redis.set(`cart-${user.id}`, cart);

    // Revalidate paths as needed
    revalidatePath("/", "layout");

    return { success: true };
  }catch (error) {
    logger.error("[updateCartActiondata] Error updating cart:", error);
    return { success: false};
  }
}

export async function decreaseItemFromCart( itemId: string ) {
  try {
    
    const user = await getCurrentDbUser()
    if (!user) {
      logger.error(`[decreaseItemFromCart] No user authenticated`);
      return { success: false };
    }

    const parse = cuidSchema.safeParse(itemId);

    if (!parse.success) {
      logger.error("[decreaseItemFromCart] Invalid product ID provided:", parse.error.format());
      return { success: false };
    }

    const validatedProductId = parse.data

    if (!redis) {
      logger.error("[decreaseItemFromCart] Redis connection not available");
      return { success: false};
    }

    // Retrieve the cart from Redis
    const cart = await redis.get<Cart>(`cart-${ user.id}`);

    if (!cart) {
      logger.error(`[decreaseItemFromCart] No cart found for user ${user.id}`);
      return { success: false };
    }

    // Find the item in the cart
    const existingItem = cart.items.find((item) => item.id === validatedProductId);

    if (!existingItem) {
      logger.error(`[decreaseItemFromCart] Item not found in cart: ${validatedProductId}`);
      return { success: false };
    }

    if (existingItem.quantity > 1) {
      // Decrease the quantity if it's greater than 1
      existingItem.quantity -= 1;
    } else {
      // Remove the item from the cart if the quantity is 1
      cart.items = cart.items.filter((item) => item.id !== validatedProductId);
    }

    // Update the cart in Redis
    await redis.set(`cart-${user.id}`, cart);

    // Revalidate paths as needed
    revalidatePath("/cart", "layout");

    return { success: true };
  } catch (error) {
    logger.error("[decreaseItemFromCart] Error decreasing item from cart:", error);
    return { success: false };
  }
}

export async function deleteItemFromCart(itemId: string) {
  try {

    if(!itemId) return { success: false };
    
    const user = await getCurrentDbUser()
    if (!user) {
      logger.error(`[deleteItemFromCart] No user authenticated`);
      return { success: false };
    }

    const parse = cuidSchema.safeParse(itemId);

    if (!parse.success) {
      logger.error("[deleteItemFromCart] Invalid product ID provided:", parse.error.format());
      return { success: false };
    }

    const validatedProductId = parse.data

    if (!redis) {
      logger.error("[deleteItemFromCart] Redis connection not available");
      return { success: false};
    }

    // Retrieve the cart from Redis
    const cart = await redis.get<Cart>(`cart-${user.id}`);

    if (!cart || !cart.items.length) {
      logger.error(`[deleteItemFromCart] No cart found for user ${user.id}`);
      return { success: false };
    }

    // Filter out the item to delete
    cart.items = cart.items.filter((item) => item.id !== validatedProductId);

    // Update the cart in Redis
    await redis.set(`cart-${user.id}`, cart);

    // Revalidate paths as needed
    revalidatePath("/cart", "layout");

    return { success: true };
  } catch (error) {
    logger.error("[deleteItemFromCart] Error deleting item from cart:", error);
    return { success: false };
  }
}

export async function addObersvationsToCart( notes: string ) {
  try {

    if(!notes) return { success: false };

    const user = await getCurrentDbUser()
    if (!user) {
      logger.error(`[addObersvationsToCart] No user authenticated`);
      return { success: false };
    }

    if (!redis) {
      logger.error("[addObersvationsToCart] Redis connection not available");
      return { success: false};
    }

    //validate with zod
    const validatedNotes = addObservationsToCartSchema.safeParse(notes);

    if (!validatedNotes.success) {
      logger.error("[addObersvationsToCart] Invalid notes provided:", validatedNotes.error.format());
      return { success: false };
    }

    const notesToUse = validatedNotes.data;


    // Retrieve the cart from Redis
    const cart = await redis.get<Cart>(`cart-${user.id}`);

    if (!cart) {
      logger.error(`[addObersvationsToCart] No cart found for user ${user.id}`);
      return { success: false };
    }

    cart.order = {
      ...cart.order,
      notes: notesToUse,
    };

    // Update the cart in Redis
    await redis.set(`cart-${user.id}`, cart);

    // Revalidate paths as needed
    revalidatePath("/cart", "layout");

    return { success: true };
  } catch (error) {
    logger.error("[addObersvationsToCart] Error adding observations to cart:", error);
    return { success: false };
  }
}