"use client"

import { ReturnItem } from "@/types/returns";
import { formatPriceRON } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import Image from "next/image";

// Return reason labels
const RETURN_REASON_LABELS = {
  wrongItem: "Produs greșit",
  defective: "Produs defect",
  damaged: "Produs deteriorat",
  notAsDescribed: "Nu corespunde descrierii",
  noLongerWanted: "Nu mai doresc produsul",
  other: "Altul",
};

// Item condition labels
const ITEM_CONDITION_LABELS = {
  asDescribed: "Conform descrierii",
  damaged: "Deteriorat",
  opened: "Deschis",
  used: "Folosit",
  missingParts: "Lipsesc piese",
};

// Inspection result labels
const INSPECTION_RESULT_LABELS = {
  approved: "Aprobat",
  rejected: "Respins",
  partiallyApproved: "Parțial aprobat",
};

interface ReturnItemCardProps {
  item: ReturnItem;
  showInspectionDetails?: boolean;
  className?: string;
}

export default function ReturnItemCard({ 
  item, 
  showInspectionDetails = false,
  className 
}: ReturnItemCardProps) {
  return (
    <Card className={className}>
      <CardContent className="p-4">
        <div className="flex items-start gap-4">
          <div className="relative h-16 w-16 flex-shrink-0">
            <Image
              src={item.orderItem.product.ImageUrl[0] || "/productDefault.jpg"}
              alt={item.orderItem.product.Description_Local}
              fill
              className="object-cover rounded-md"
            />
          </div>

          <div className="flex-1 space-y-2">
            <div>
              <h4 className="font-medium text-sm">
                {item.orderItem.product.Description_Local}
              </h4>
              <p className="text-xs text-muted-foreground">
                Cod: {item.orderItem.product.Material_Number}
              </p>
            </div>

            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <span>Cantitate: {item.quantity}</span>
              <span>Preț: {formatPriceRON(item.orderItem.price)}</span>
            </div>

            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                {RETURN_REASON_LABELS[item.reason]}
              </Badge>
              <Badge variant="secondary" className="text-xs">
                {ITEM_CONDITION_LABELS[item.condition]}
              </Badge>
            </div>

            {item.description && (
              <p className="text-xs text-muted-foreground italic bg-muted p-2 rounded">
                {item.description}
              </p>
            )}

            {showInspectionDetails && (
              <div className="space-y-2 pt-2 border-t">
                <div className="flex items-center gap-4 text-xs">
                  <span className={`font-medium ${
                    item.isReceived ? 'text-green-600' : 'text-muted-foreground'
                  }`}>
                    {item.isReceived ? '✓ Primit' : '○ În așteptare'}
                  </span>
                  <span className={`font-medium ${
                    item.isInspected ? 'text-green-600' : 'text-muted-foreground'
                  }`}>
                    {item.isInspected ? '✓ Verificat' : '○ Neverificat'}
                  </span>
                </div>

                {item.inspectionResult && (
                  <div className="space-y-1">
                    <Badge 
                      variant={
                        item.inspectionResult === 'approved' ? 'default' :
                        item.inspectionResult === 'rejected' ? 'destructive' :
                        'secondary'
                      }
                      className="text-xs"
                    >
                      {INSPECTION_RESULT_LABELS[item.inspectionResult]}
                    </Badge>
                    {item.inspectionNotes && (
                      <p className="text-xs text-muted-foreground bg-muted p-2 rounded">
                        <strong>Note verificare:</strong> {item.inspectionNotes}
                      </p>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Export the label mappings for use in other components
export { 
  RETURN_REASON_LABELS, 
  ITEM_CONDITION_LABELS, 
  INSPECTION_RESULT_LABELS 
};
