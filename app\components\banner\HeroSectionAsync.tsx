// components/sections/HeroSectionAsync.tsx
import { Suspense } from 'react';
import HeroSection from './HeroSection';
import { getHeroBanners } from '@/app/getData/banners';
import { HeroSkeleton } from './HeroBannerSkeleton';

async function HeroSectionData() {
  const heroBanners = await getHeroBanners();

  return <HeroSection heroBanners={heroBanners} />;
}

export default function HeroSectionAsync() {
  return (
    <Suspense fallback={<HeroSkeleton />}>
      <HeroSectionData />
    </Suspense>
  );
}