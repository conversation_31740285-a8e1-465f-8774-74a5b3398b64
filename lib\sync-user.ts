import { Prisma, User } from "@/generated/prisma";
import { prisma, withRetry } from "./db";
import { logger } from "./logger";

interface FindOrCreateUserParams {
  externalId: string;
  email: string;
  firstName?: string;
  lastName?: string;
  profileImage?: string;
  updateLoginStats?: boolean;
  twoFactorEnabled?: boolean;
  phoneNumber?: string;
  mfaEnabledAt?: Date | null;
  mfaDisabledAt?: Date | null;
  passwordEnabled?: boolean | null;
  totpEnabled?: boolean;
  emailVerified?: Date | null;
}

export async function findOrCreateUser(params: FindOrCreateUserParams): Promise<User | null> {
  const { 
    externalId, 
    email, 
    firstName, 
    lastName, 
    profileImage, 
    updateLoginStats, 
    twoFactorEnabled, 
    phoneNumber, 
    mfaEnabledAt, 
    mfaDisabledAt, 
    passwordEnabled, 
    totpEnabled, 
    emailVerified 
  } = params;

  // Input validation
  if (!externalId || !email) {
    logger.warn('[findOrCreateUser] Missing required fields: externalId or email');
    return null;
  }

  try {
    const dbUser = await withRetry(() =>
      prisma.$transaction(async (tx) => {
        // First try to find by externalId
        let user = await tx.user.findUnique({ 
          where: { externalId },
        });

        if (user) {
          // User exists, update login stats and MFA status if needed
          type UserUpdateData = Prisma.UserUpdateInput;
          const updateData: UserUpdateData = {};

          if (updateLoginStats) {
            updateData.lastLoginAt = new Date();
            updateData.loginCount = { increment: 1 };
            updateData.lastActivityAt = new Date();
          }

          // Always sync MFA status from Clerk if provided
          if (twoFactorEnabled !== undefined) {
            updateData.twoFactorEnabled = twoFactorEnabled;
          }

          if (phoneNumber) {
            updateData.phoneNumber = phoneNumber;
          }

          if (mfaEnabledAt) {
            updateData.mfaEnabledAt = mfaEnabledAt;
          }

          if (mfaDisabledAt) {
            updateData.mfaDisabledAt = mfaDisabledAt;
          }

          if (passwordEnabled) {
            updateData.passwordEnabled = passwordEnabled;
          }

          if (totpEnabled !== undefined) {
            updateData.totpEnabled = totpEnabled;
          }

          if (emailVerified) {
            updateData.emailVerified = emailVerified;
          }

          // Only update if there's data to update
          if (Object.keys(updateData).length > 0) {
            user = await tx.user.update({
              where: { id: user.id },
              data: updateData,
            });
          }

          return user;
        }

        // Check if user exists by email
        const existingUserByEmail = await tx.user.findUnique({ 
          where: { email } 
        });

        if (existingUserByEmail) {
          // Security check: Only merge if same provider or no provider
          if (!existingUserByEmail.externalProvider || existingUserByEmail.externalProvider === 'clerk') {
            return await tx.user.update({
              where: { id: existingUserByEmail.id },
              data: {
                externalId,
                externalProvider: "clerk",
                firstName: firstName || existingUserByEmail.firstName,
                lastName: lastName || existingUserByEmail.lastName,
                profileImage: profileImage || existingUserByEmail.profileImage,
                phoneNumber: phoneNumber || existingUserByEmail.phoneNumber,
                twoFactorEnabled: twoFactorEnabled ?? existingUserByEmail.twoFactorEnabled,
                mfaEnabledAt: mfaEnabledAt ?? existingUserByEmail.mfaEnabledAt,
                mfaDisabledAt: mfaDisabledAt ?? existingUserByEmail.mfaDisabledAt,
                passwordEnabled: passwordEnabled ?? existingUserByEmail.passwordEnabled,
                totpEnabled: totpEnabled ?? existingUserByEmail.totpEnabled,
                emailVerified: emailVerified ?? existingUserByEmail.emailVerified,
              },
            });
          } else {
            logger.warn(`[findOrCreateUser] Email ${email} already exists with different provider: ${existingUserByEmail.externalProvider}`);
            return null;
          }
        }

        // Create new user
        return await tx.user.create({
          data: {
            externalId,
            email,
            firstName: firstName || "",
            lastName: lastName || "",
            profileImage: profileImage || "",
            externalProvider: "clerk",
            twoFactorEnabled: twoFactorEnabled || false,
            lastLoginAt: updateLoginStats ? new Date() : undefined,
            loginCount: updateLoginStats ? 1 : 0,
            lastActivityAt: updateLoginStats ? new Date() : undefined,
            phoneNumber: phoneNumber || undefined,
            mfaEnabledAt: mfaEnabledAt || undefined,
            mfaDisabledAt: mfaDisabledAt || undefined,
            passwordEnabled: passwordEnabled || undefined,
            totpEnabled: totpEnabled || false,
            isActive: true,
            emailVerified: emailVerified || undefined,
          },
        });
      }, {
        timeout: 10000, // Add transaction timeout
      })
    );

    return dbUser;
  } catch (error) {
    logger.error('[findOrCreateUser] Error:', error);
    
    // Don't expose internal errors to client
    if (error instanceof Error && error.message.includes('different provider')) {
      return null;
    }
    
    return null;
  }
}
// export async function findOrCreateUser(params: {
//   externalId: string;
//   email: string;
//   firstName?: string;
//   lastName?: string;
//   profileImage?: string;
//   updateLoginStats?: boolean;
//   twoFactorEnabled?: boolean;
//   phoneNumber?: string;
//   mfaEnabledAt?: Date | null;
//   mfaDisabledAt?: Date | null;
//   passwordEnabled?: boolean | null;
//   totpEnabled?: boolean;
//   emailVerified?: Date | null;
// }): Promise<User | null> {
//   const { externalId, email, firstName, lastName, profileImage, updateLoginStats, twoFactorEnabled, phoneNumber, mfaEnabledAt, mfaDisabledAt, passwordEnabled, totpEnabled, emailVerified } = params;

//   // Input validation
//   if (!externalId || !email) {
//     logger.warn('[findOrCreateUser] Missing required fields: externalId or email');
//     return null;
//   }

//   try {
//     const dbUser = await withRetry(() =>
//       prisma.$transaction(async (tx) => {
//         // First try to find by externalId
//         let user = await tx.user.findUnique({ 
//           where: { externalId } 
//         });

//         if (user) {
//           // User exists, update login stats and MFA status if needed
//           const updateData: any = {};

//           if (updateLoginStats) {
//             updateData.lastLoginAt = new Date();
//             updateData.loginCount = { increment: 1 };
//             updateData.lastActivityAt = new Date();
//           }

//           // Always sync MFA status from Clerk if provided
//           if (twoFactorEnabled !== undefined) {
//             updateData.twoFactorEnabled = twoFactorEnabled;
//           }

//           if (phoneNumber) {
//             updateData.phoneNumber = phoneNumber;
//           }

//           if (mfaEnabledAt) {
//             console.log("Updating mfaEnabledAt", mfaEnabledAt);
//             updateData.mfaEnabledAt = mfaEnabledAt;
//           }

//           if (mfaDisabledAt) {
//             updateData.mfaDisabledAt = mfaDisabledAt;
//           }

//           if (passwordEnabled) {
//             updateData.passwordEnabled = passwordEnabled;
//           }

//           if (totpEnabled !== undefined) {
//             updateData.totpEnabled = totpEnabled;
//           }

//           if (emailVerified) {
//             updateData.emailVerified = emailVerified;
//           }

//           // Only update if there's data to update
//           if (Object.keys(updateData).length > 0) {
//             user = await tx.user.update({
//               where: { id: user.id },
//               data: updateData,
//             });
//           }

//           return user;
//         }

//         // Check if user exists by email
//         const existingUserByEmail = await tx.user.findUnique({ 
//           where: { email } 
//         });

//         if (existingUserByEmail) {
//           // Security check: Only merge if same provider or no provider
//           if (!existingUserByEmail.externalProvider || existingUserByEmail.externalProvider === 'clerk') {
//             return await tx.user.update({
//               where: { id: existingUserByEmail.id },
//               data: {
//                 externalId,
//                 externalProvider: "clerk",
//                 firstName: firstName || existingUserByEmail.firstName,
//                 lastName: lastName || existingUserByEmail.lastName,
//                 profileImage: profileImage || existingUserByEmail.profileImage,
//                 phoneNumber: phoneNumber || existingUserByEmail.phoneNumber,
//                 twoFactorEnabled: twoFactorEnabled || existingUserByEmail.twoFactorEnabled,
//                 mfaEnabledAt: mfaEnabledAt || existingUserByEmail.mfaEnabledAt,
//                 mfaDisabledAt: mfaDisabledAt || existingUserByEmail.mfaDisabledAt,
//                 passwordEnabled: passwordEnabled || existingUserByEmail.passwordEnabled,
//                 totpEnabled: totpEnabled || existingUserByEmail.totpEnabled,
//                 emailVerified: emailVerified || existingUserByEmail.emailVerified,
//               },
//             });
//           } else {
//             logger.warn(`[findOrCreateUser] Email ${email} already exists with different provider: ${existingUserByEmail.externalProvider}`);
//             //throw new Error('Email already exists with different provider');
//             return null
//           }
//         }

//         // Create new user
//         return await tx.user.create({
//           data: {
//             externalId,
//             email,
//             firstName: firstName || "",
//             lastName: lastName || "",
//             profileImage: profileImage || "",
//             externalProvider: "clerk",
//             twoFactorEnabled: twoFactorEnabled || false,
//             lastLoginAt: updateLoginStats ? new Date() : undefined,
//             loginCount: updateLoginStats ? 1 : 0,
//             lastActivityAt: updateLoginStats ? new Date() : undefined,
//             phoneNumber: phoneNumber || undefined,
//             mfaEnabledAt: mfaEnabledAt || undefined,
//             mfaDisabledAt: mfaDisabledAt || undefined,
//             passwordEnabled: passwordEnabled || undefined,
//             totpEnabled: totpEnabled || false,
//             isActive: true,
//             emailVerified: emailVerified || undefined,
//           },
//         });
//       }, {
//         timeout: 10000, // Add transaction timeout
//       })
//     );

//     return dbUser;
//   } catch (error) {
//     logger.error('[findOrCreateUser] Error:', error);
    
//     // Don't expose internal errors to client
//     if (error instanceof Error && error.message.includes('different provider')) {
//       //throw error; // Re-throw security errors
//       return null
//     }
    
//     return null;
//   }
// }

export async function updateExpiredLockouts(): Promise<void> {
  const now = new Date();
  console.log("updateExpiredLockouts called");
  try {
    const expiredLockouts = await prisma.user.updateMany({
      where: {
        isSuspended: true,
        lockoutUntil: {
          not: null,
          lte: now
        }
      },
      data: {
        isSuspended: false,
        lockoutUntil: null
      }
    });

    if (expiredLockouts.count > 0) {
      logger.info(`[updateExpiredLockouts] Updated ${expiredLockouts.count} expired lockouts`);
      
      // Optionally create audit logs for expired lockouts
      const users = await prisma.user.findMany({
        where: {
          isSuspended: false,
          lockoutUntil: null,
          updatedAt: {
            gte: new Date(now.getTime() - 1000) // Users updated in the last second
          }
        }
      });

      for (const user of users) {
        await prisma.userAuditLog.create({
          data: {
            userId: user.id,
            action: 'account.lockout_expired',
            entityType: 'user',
            entityId: user.externalId || user.id,
            details: JSON.stringify({
              message: 'Account lockout expired automatically',
              timestamp: now.toISOString()
            }),
            performedBy: 'system',
            createdAt: now
          }
        });
      }
    }
  } catch (error) {
    logger.error('[updateExpiredLockouts] Error updating expired lockouts:', error);
  }
}