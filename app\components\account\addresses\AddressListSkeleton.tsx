import { Skeleton } from "@/components/ui/skeleton";

interface AddressListSkeletonProps {
  count?: number;
}

export default function AddressListSkeleton({ count = 3 }: AddressListSkeletonProps) {
  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Header Skeleton */}
      <div className="flex justify-between items-start mb-8">
        <div className="space-y-2">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-96" />
        </div>
        <Skeleton className="h-10 w-32" />
      </div>

      {/* Address List Skeleton */}
      <div className="border rounded-lg overflow-hidden shadow">
        {Array.from({ length: count }).map((_, index) => (
          <div key={index} className="flex items-center gap-6 p-6 border-b last:border-0">
            {/* Icon Skeleton */}
            <Skeleton className="w-12 h-12 rounded-lg flex-shrink-0" />

            {/* Address Details Skeleton */}
            <div className="flex-1 space-y-3">
              <div className="flex items-center gap-2">
                <Skeleton className="h-6 w-40" />
                <Skeleton className="h-5 w-16 rounded-full" />
              </div>

              <div className="space-y-2">
                <div className="flex items-start gap-2">
                  <Skeleton className="h-4 w-4 mt-0.5 flex-shrink-0" />
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-64" />
                    <Skeleton className="h-4 w-48" />
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Skeleton className="h-4 w-4 flex-shrink-0" />
                  <Skeleton className="h-4 w-32" />
                </div>

                <div className="flex items-center gap-2">
                  <Skeleton className="h-4 w-4 flex-shrink-0" />
                  <Skeleton className="h-4 w-40" />
                </div>
              </div>
            </div>

            {/* Actions Skeleton */}
            <div className="flex items-center gap-2">
              <Skeleton className="h-8 w-32" />
              <Skeleton className="h-8 w-8" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
