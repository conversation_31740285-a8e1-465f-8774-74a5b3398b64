"use client"

import { ReturnTimelineEvent } from "@/types/returns";
import { formatDate } from "@/lib/order-utils";
import { RETURN_STATUS_CONFIG } from "./ReturnStatusBadge";

interface ReturnTimelineProps {
  events: ReturnTimelineEvent[];
  className?: string;
}

export default function ReturnTimeline({ events, className }: ReturnTimelineProps) {
  const getTimelineIcon = (event: ReturnTimelineEvent) => {
    const config = RETURN_STATUS_CONFIG[event.status];
    const Icon = config.icon;
    
    return (
      <div className={`flex h-8 w-8 items-center justify-center rounded-full border-2 ${
        event.isCompleted 
          ? 'bg-primary border-primary text-primary-foreground' 
          : event.isCurrent
          ? 'bg-secondary border-secondary text-secondary-foreground'
          : 'bg-background border-muted-foreground text-muted-foreground'
      }`}>
        <Icon className="h-4 w-4" />
      </div>
    );
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {events.map((event, index) => (
        <div key={index} className="flex items-start gap-4">
          {getTimelineIcon(event)}
          <div className="flex-1 space-y-1">
            <div className="flex items-center justify-between">
              <h4 className={`text-sm font-medium ${
                event.isCompleted ? 'text-foreground' : 'text-muted-foreground'
              }`}>
                {event.title}
              </h4>
              <span className="text-xs text-muted-foreground">
                {formatDate(event.date)}
              </span>
            </div>
            <p className={`text-xs ${
              event.isCompleted ? 'text-muted-foreground' : 'text-muted-foreground/70'
            }`}>
              {event.description}
            </p>
          </div>
          {/* Connector line */}
          {index < events.length - 1 && (
            <div className="absolute left-4 mt-8 h-6 w-px bg-border" />
          )}
        </div>
      ))}
    </div>
  );
}
