'use client';

import { Component, ReactNode } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertTriangle, RefreshCw, Home } from "lucide-react";
import Link from "next/link";

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ServiceErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Service Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-[400px] flex items-center justify-center p-4">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="mx-auto w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mb-4">
                <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
              <CardTitle className="text-xl text-gray-900 dark:text-gray-100">
                Oops! Ceva nu a mers bine
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <p className="text-gray-600 dark:text-gray-400">
                A apărut o eroare neașteptată în secțiunea de servicii. 
                Te rugăm să încerci din nou sau să contactezi suportul dacă problema persistă.
              </p>
              
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <div className="text-left bg-gray-100 dark:bg-gray-800 p-3 rounded-md text-xs font-mono text-red-600 dark:text-red-400 overflow-auto max-h-32">
                  {this.state.error.message}
                  {this.state.error.stack && (
                    <pre className="mt-2 text-xs">{this.state.error.stack}</pre>
                  )}
                </div>
              )}

              <div className="flex flex-col sm:flex-row gap-3 pt-4">
                <Button
                  onClick={() => window.location.reload()}
                  className="flex items-center gap-2"
                  variant="default"
                >
                  <RefreshCw className="w-4 h-4" />
                  Încearcă din nou
                </Button>
                
                <Button
                  asChild
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <Link href="/account">
                    <Home className="w-4 h-4" />
                    Înapoi la cont
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// Functional error component for specific service errors
export function ServiceError({ 
  title = "Eroare la încărcarea serviciilor",
  message = "Nu am putut încărca informațiile despre servicii. Te rugăm să încerci din nou.",
  onRetry,
  showHomeButton = true
}: {
  title?: string;
  message?: string;
  onRetry?: () => void;
  showHomeButton?: boolean;
}) {
  return (
    <div className="min-h-[400px] flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-orange-100 dark:bg-orange-900/20 rounded-full flex items-center justify-center mb-4">
            <AlertTriangle className="w-6 h-6 text-orange-600 dark:text-orange-400" />
          </div>
          <CardTitle className="text-xl text-gray-900 dark:text-gray-100">
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-gray-600 dark:text-gray-400">
            {message}
          </p>

          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            {onRetry && (
              <Button
                onClick={onRetry}
                className="flex items-center gap-2"
                variant="default"
              >
                <RefreshCw className="w-4 h-4" />
                Încearcă din nou
              </Button>
            )}
            
            {showHomeButton && (
              <Button
                asChild
                variant="outline"
                className="flex items-center gap-2"
              >
                <Link href="/account">
                  <Home className="w-4 h-4" />
                  Înapoi la cont
                </Link>
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Empty state component
export function ServiceEmptyState({
  title = "Nu ai cereri de service",
  message = "Creează prima ta cerere de service pentru a începe.",
  onCreateNew,
  showCreateButton = true
}: {
  title?: string;
  message?: string;
  onCreateNew?: () => void;
  showCreateButton?: boolean;
}) {
  return (
    <div className="text-center py-12">
      <div className="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-6">
        <AlertTriangle className="w-8 h-8 text-gray-400" />
      </div>
      
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
        {title}
      </h3>
      
      <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
        {message}
      </p>
      
      {showCreateButton && onCreateNew && (
        <Button onClick={onCreateNew} className="flex items-center gap-2 mx-auto">
          <AlertTriangle className="w-4 h-4" />
          Creează cerere de service
        </Button>
      )}
    </div>
  );
}
