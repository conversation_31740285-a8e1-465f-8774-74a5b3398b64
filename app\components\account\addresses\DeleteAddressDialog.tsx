"use client";

import { useTransition } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Loader2 } from "lucide-react";
import { BillingAddress, ShippingAddress, AddressType } from "@/types/addresses";
import { deleteBillingAddress, deleteShippingAddress } from "@/app/actions/addresses";
import { toast } from "sonner";

interface DeleteAddressDialogProps {
  isOpen: boolean;
  onClose: () => void;
  address?: BillingAddress | ShippingAddress;
  type: AddressType;
}

export default function DeleteAddressDialog({ 
  isOpen, 
  onClose, 
  address, 
  type 
}: DeleteAddressDialogProps) {
  const [isPending, startTransition] = useTransition();

  const handleDelete = () => {
    if (!address) return;

    startTransition(async () => {
      const result = type === 'billing' 
        ? await deleteBillingAddress(address.id)
        : await deleteShippingAddress(address.id);

      if (result.success) {
        toast.success(
          type === 'billing' 
            ? 'Adresa de facturare a fost ștearsă cu succes'
            : 'Adresa de livrare a fost ștearsă cu succes'
        );
        onClose();
      } else {
        toast.error(result.error || 'A apărut o eroare la ștergerea adresei');
      }
    });
  };

  if (!address) return null;

  const isBilling = type === 'billing';

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            Ștergeți adresa de {isBilling ? 'facturare' : 'livrare'}?
          </AlertDialogTitle>
          <AlertDialogDescription className="space-y-2">
            <p>
              Sunteți sigur că doriți să ștergeți această adresă? Această acțiune nu poate fi anulată.
            </p>
            <div className="bg-muted p-3 rounded-md mt-3">
              <p className="font-medium">{address.fullName}</p>
              <p className="text-sm text-muted-foreground">
                {address.address}, {address.city}, {address.county}
              </p>
              {address.isDefault && (
                <p className="text-sm text-amber-600 font-medium mt-1">
                  ⚠️ Aceasta este adresa implicită
                </p>
              )}
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isPending}>
            Anulează
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isPending}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
            Șterge adresa
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
