"use client";

import { useTransition } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Loader2 } from "lucide-react";
import { 
  billingAddressSchema, 
  shippingAddressSchema,
  type BillingAddressInput,
  type ShippingAddressInput 
} from "@/lib/zod";
import { BillingAddress, ShippingAddress, AddressType } from "@/types/addresses";
import { 
  createBillingAddress,
  updateBillingAddress,
  createShippingAddress,
  updateShippingAddress
} from "@/app/actions/addresses";
import { toast } from "sonner";

interface AddressFormProps {
  type: AddressType;
  mode: 'create' | 'edit';
  address?: BillingAddress | ShippingAddress;
  onSuccess: () => void;
  onCancel: () => void;
}

export default function AddressForm({ type, mode, address, onSuccess, onCancel }: AddressFormProps) {
  if (type === 'billing') {
    return (
      <BillingAddressForm
        mode={mode}
        address={address as BillingAddress | undefined}
        onSuccess={onSuccess}
        onCancel={onCancel}
      />
    );
  } else {
    return (
      <ShippingAddressForm
        mode={mode}
        address={address as ShippingAddress | undefined}
        onSuccess={onSuccess}
        onCancel={onCancel}
      />
    );
  }
}

function BillingAddressForm({
  mode,
  address,
  onSuccess,
  onCancel
}: {
  mode: 'create' | 'edit';
  address?: BillingAddress;
  onSuccess: () => void;
  onCancel: () => void;
}) {
  const [isPending, startTransition] = useTransition();

  const getDefaultValues = (): BillingAddressInput => {
    if (mode === 'edit' && address) {
      return {
        fullName: address.fullName,
        companyName: address.companyName || '',
        address: address.address,
        city: address.city,
        county: address.county,
        cui: address.cui || '',
        bank: address.bank || '',
        iban: address.iban || '',
      };
    }

    return {
      fullName: '',
      companyName: '',
      address: '',
      city: '',
      county: '',
      cui: '',
      bank: '',
      iban: '',
    };
  };

  const {
    register,
    handleSubmit,
    formState: { errors, isDirty }
  } = useForm<BillingAddressInput>({
    resolver: zodResolver(billingAddressSchema),
    defaultValues: getDefaultValues()
  });

  const onSubmit = (data: BillingAddressInput) => {
    startTransition(async () => {
      let result;

      if (mode === 'create') {
        result = await createBillingAddress(data);
      } else if (address) {
        result = await updateBillingAddress(address.id, data);
      }

      if (result?.success) {
        toast.success(
          mode === 'create'
            ? 'Adresa de facturare a fost creată cu succes'
            : 'Adresa de facturare a fost actualizată cu succes'
        );
        onSuccess();
      } else {
        toast.error(result?.error || 'A apărut o eroare');
      }
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      {/* Full Name */}
      <div className="space-y-2">
        <Label htmlFor="fullName">Nume complet *</Label>
        <Input
          id="fullName"
          {...register("fullName")}
          className={errors.fullName ? "border-red-500" : ""}
          disabled={isPending}
          placeholder="Introduceți numele complet"
        />
        {errors.fullName && (
          <p className="text-sm text-red-600">{errors.fullName.message}</p>
        )}
      </div>

      {/* Company Name */}
      <div className="space-y-2">
        <Label htmlFor="companyName">Nume companie</Label>
        <Input
          id="companyName"
          {...register("companyName")}
          className={errors.companyName ? "border-red-500" : ""}
          disabled={isPending}
          placeholder="Introduceți numele companiei (opțional)"
        />
        {errors.companyName && (
          <p className="text-sm text-red-600">{errors.companyName.message}</p>
        )}
      </div>

      {/* Address */}
      <div className="space-y-2">
        <Label htmlFor="address">Adresa *</Label>
        <Input
          id="address"
          {...register("address")}
          className={errors.address ? "border-red-500" : ""}
          disabled={isPending}
          placeholder="Introduceți adresa completă"
        />
        {errors.address && (
          <p className="text-sm text-red-600">{errors.address.message}</p>
        )}
      </div>

      {/* City and County */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="city">Oraș *</Label>
          <Input
            id="city"
            {...register("city")}
            className={errors.city ? "border-red-500" : ""}
            disabled={isPending}
            placeholder="Introduceți orașul"
          />
          {errors.city && (
            <p className="text-sm text-red-600">{errors.city.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="county">Județ *</Label>
          <Input
            id="county"
            {...register("county")}
            className={errors.county ? "border-red-500" : ""}
            disabled={isPending}
            placeholder="Introduceți județul"
          />
          {errors.county && (
            <p className="text-sm text-red-600">{errors.county.message}</p>
          )}
        </div>
      </div>

      {/* CUI */}
      <div className="space-y-2">
        <Label htmlFor="cui">CUI</Label>
        <Input
          id="cui"
          {...register("cui")}
          className={errors.cui ? "border-red-500" : ""}
          disabled={isPending}
          placeholder="ex: RO12345678"
        />
        {errors.cui && (
          <p className="text-sm text-red-600">{errors.cui.message}</p>
        )}
      </div>

      {/* Bank */}
      <div className="space-y-2">
        <Label htmlFor="bank">Bancă</Label>
        <Input
          id="bank"
          {...register("bank")}
          className={errors.bank ? "border-red-500" : ""}
          disabled={isPending}
          placeholder="Introduceți numele băncii"
        />
        {errors.bank && (
          <p className="text-sm text-red-600">{errors.bank.message}</p>
        )}
      </div>

      {/* IBAN */}
      <div className="space-y-2">
        <Label htmlFor="iban">IBAN</Label>
        <Input
          id="iban"
          {...register("iban")}
          className={errors.iban ? "border-red-500" : ""}
          disabled={isPending}
          placeholder="ex: ************************"
        />
        {errors.iban && (
          <p className="text-sm text-red-600">{errors.iban.message}</p>
        )}
      </div>

      {/* Form Actions */}
      <div className="flex justify-end gap-3 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isPending}
        >
          Anulează
        </Button>
        <Button
          type="submit"
          disabled={isPending || !isDirty}
        >
          {isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
          {mode === 'create' ? 'Creează adresa' : 'Actualizează adresa'}
        </Button>
      </div>
    </form>
  );
}

function ShippingAddressForm({
  mode,
  address,
  onSuccess,
  onCancel
}: {
  mode: 'create' | 'edit';
  address?: ShippingAddress;
  onSuccess: () => void;
  onCancel: () => void;
}) {
  const [isPending, startTransition] = useTransition();

  const getDefaultValues = (): ShippingAddressInput => {
    if (mode === 'edit' && address) {
      return {
        fullName: address.fullName,
        address: address.address,
        city: address.city,
        county: address.county,
        phoneNumber: address.phoneNumber,
        notes: address.notes || '',
      };
    }

    return {
      fullName: '',
      address: '',
      city: '',
      county: '',
      phoneNumber: '',
      notes: '',
    };
  };

  const {
    register,
    handleSubmit,
    formState: { errors, isDirty }
  } = useForm<ShippingAddressInput>({
    resolver: zodResolver(shippingAddressSchema),
    defaultValues: getDefaultValues()
  });

  const onSubmit = (data: ShippingAddressInput) => {
    startTransition(async () => {
      let result;

      if (mode === 'create') {
        result = await createShippingAddress(data);
      } else if (address) {
        result = await updateShippingAddress(address.id, data);
      }

      if (result?.success) {
        toast.success(
          mode === 'create'
            ? 'Adresa de livrare a fost creată cu succes'
            : 'Adresa de livrare a fost actualizată cu succes'
        );
        onSuccess();
      } else {
        toast.error(result?.error || 'A apărut o eroare');
      }
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      {/* Full Name */}
      <div className="space-y-2">
        <Label htmlFor="fullName">Nume complet *</Label>
        <Input
          id="fullName"
          {...register("fullName")}
          className={errors.fullName ? "border-red-500" : ""}
          disabled={isPending}
          placeholder="Introduceți numele complet"
        />
        {errors.fullName && (
          <p className="text-sm text-red-600">{errors.fullName.message}</p>
        )}
      </div>

      {/* Address */}
      <div className="space-y-2">
        <Label htmlFor="address">Adresa *</Label>
        <Input
          id="address"
          {...register("address")}
          className={errors.address ? "border-red-500" : ""}
          disabled={isPending}
          placeholder="Introduceți adresa completă"
        />
        {errors.address && (
          <p className="text-sm text-red-600">{errors.address.message}</p>
        )}
      </div>

      {/* City and County */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="city">Oraș *</Label>
          <Input
            id="city"
            {...register("city")}
            className={errors.city ? "border-red-500" : ""}
            disabled={isPending}
            placeholder="Introduceți orașul"
          />
          {errors.city && (
            <p className="text-sm text-red-600">{errors.city.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="county">Județ *</Label>
          <Input
            id="county"
            {...register("county")}
            className={errors.county ? "border-red-500" : ""}
            disabled={isPending}
            placeholder="Introduceți județul"
          />
          {errors.county && (
            <p className="text-sm text-red-600">{errors.county.message}</p>
          )}
        </div>
      </div>

      {/* Phone Number */}
      <div className="space-y-2">
        <Label htmlFor="phoneNumber">Număr de telefon *</Label>
        <Input
          id="phoneNumber"
          {...register("phoneNumber")}
          className={errors.phoneNumber ? "border-red-500" : ""}
          disabled={isPending}
          placeholder="ex: +40712345678"
        />
        {errors.phoneNumber && (
          <p className="text-sm text-red-600">{errors.phoneNumber.message}</p>
        )}
      </div>

      {/* Notes */}
      <div className="space-y-2">
        <Label htmlFor="notes">Note</Label>
        <Textarea
          id="notes"
          {...register("notes")}
          className={errors.notes ? "border-red-500" : ""}
          disabled={isPending}
          placeholder="Instrucțiuni speciale pentru livrare (opțional)"
          rows={3}
        />
        {errors.notes && (
          <p className="text-sm text-red-600">{errors.notes.message}</p>
        )}
      </div>

      {/* Form Actions */}
      <div className="flex justify-end gap-3 pt-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isPending}
        >
          Anulează
        </Button>
        <Button
          type="submit"
          disabled={isPending || !isDirty}
        >
          {isPending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
          {mode === 'create' ? 'Creează adresa' : 'Actualizează adresa'}
        </Button>
      </div>
    </form>
  );
}
