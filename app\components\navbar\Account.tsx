"server-only"

import {
  <PERSON>,
  User,
  ShoppingBag,
  UserIcon,
  Truck,
  CreditCard,
  Refresh<PERSON><PERSON>,
  Wrench,
} from "lucide-react";

import { Button } from "@/components/ui/button";
import Link from "next/link";
import { SignOutWithLoading } from "./SignOutWithLoading";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";

type AccountComponentProps = {
  firstName: string;
  email: string;
  profileImage: string;
}

export function AccountComponent({ firstName, email, profileImage }: AccountComponentProps) {
  return (
    <>
    {/* <div className="relative group">
      <Button
        variant="ghost"
        size="icon"
        className="hover:bg-gray-100 dark:hover:bg-gray-800"
      >
        <Avatar className="h-8 w-8">
          <AvatarImage 
            src={profileImage} 
            alt="User avatar" 
          />
          <AvatarFallback className="dark:text-gray-300">
            <UserIcon className="h-6 w-6 text-gray-600 dark:text-gray-300" />
          </AvatarFallback>
        </Avatar>
      </Button>
      
      <div className="absolute right-0 mt-2 w-64 py-3 rounded-lg shadow-xl border border-gray-100 dark:border-gray-700 
        bg-white dark:bg-gray-800 opacity-0 invisible 
        group-hover:opacity-100 group-hover:visible transition-all duration-200 transform group-hover:translate-y-0 translate-y-2 divide-y divide-gray-100 dark:divide-gray-700 z-[100]">
        <div className="px-4 py-2">
          <p className="text-sm font-medium text-gray-900 dark:text-gray-100">Salut {firstName || 'Unnamed User'}!</p>
          <p className="text-xs text-gray-500 dark:text-gray-400">{email}</p>
        </div>

        <div className="py-2">
          <Link
            href="/account/orders"
            className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2"
          >
            <ShoppingBag className="w-4 h-4 text-gray-400 dark:text-gray-500" />
            Comenzile mele
          </Link>
          <Link
            href="/account/wishlist"
            className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2"
          >
            <Heart className="w-4 h-4 text-gray-400 dark:text-gray-500" />
            Favorite
          </Link>
          <Link
            href="/account/settings"
            className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2"
          >
            <User className="w-4 h-4 text-gray-400 dark:text-gray-500" />
            Profil
          </Link>
        </div>

        <div className="py-2">
          <SignOutWithLoading />
        </div>
      </div>
    </div> */}

    <HoverCard openDelay={200} closeDelay={100}>
      <HoverCardTrigger asChild>
        <Link href="/account/settings">
          <Button
            variant="ghost"
            size="icon"
            className="hover:bg-gray-100 dark:hover:bg-gray-800"
          >
            {/* <User className="h-6 w-6 text-[#4D4D4D] dark:text-gray-300" /> */}
            <Avatar className="h-8 w-8">
              <AvatarImage 
                src={profileImage} 
                alt="User avatar" 
              />
              <AvatarFallback className="dark:text-gray-300">
                <UserIcon className="h-6 w-6 text-gray-600 dark:text-gray-300" />
              </AvatarFallback>
            </Avatar>
          </Button>
        </Link>
      </HoverCardTrigger>

      <HoverCardContent className="w-auto p-0 border-none" align="end" sideOffset={5}>

        <div className="px-4 py-2">
          <p className="text-sm font-medium text-gray-900 dark:text-gray-100">Salut {firstName || 'Unnamed User'}!</p>
          <p className="text-xs text-gray-500 dark:text-gray-400">{email}</p>
        </div>

        <div className="py-2">
          <Link
            href="/account/orders"
            className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2"
          >
            <ShoppingBag className="w-4 h-4 text-gray-400 dark:text-gray-500" />
            Comenzile mele
          </Link>
          <Link
            href="/account/wishlist"
            className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2"
          >
            <Heart className="w-4 h-4 text-gray-400 dark:text-gray-500" />
            Favorite
          </Link>
          <Link
            href="/account/settings"
            className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2"
          >
            <User className="w-4 h-4 text-gray-400 dark:text-gray-500" />
            Profil
          </Link>
          {/* Shipping addresses */}
          <Link
            href="/account/shipping"
            className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2"
          >
            <Truck className="w-4 h-4 text-gray-400 dark:text-gray-500" />
            Adrese de livrare
          </Link>
          {/* Billing addresses */}
          <Link
            href="/account/billing"
            className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2"
          >
            <CreditCard className="w-4 h-4 text-gray-400 dark:text-gray-500" />
            Facturare
          </Link>
          {/* Returns */}
          <Link
            href="/account/returns"
            className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4 text-gray-400 dark:text-gray-500" />
            Retururi
          </Link>
          {/* Service & Repairs */}
          <Link
            href="/account/service"
            className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2"
          >
            <Wrench className="w-4 h-4 text-gray-400 dark:text-gray-500" />
            Servicii & Reparari
          </Link>
        </div>

        <div className="py-2">
          <SignOutWithLoading />
        </div>

      </HoverCardContent>
    </HoverCard>
      </>
  );
}
