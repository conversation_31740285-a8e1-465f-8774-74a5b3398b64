"use client";

import { placeOrder } from "@/app/actions/order";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { useTransition } from "react";
import { toast } from "sonner";
import { ShippingSelection } from "@/types/addresses";

interface CartButtonOrderProps {
  shippingSelection?: ShippingSelection;
  acceptTerms: boolean;
}

export function CartButtonOrder({ shippingSelection, acceptTerms }: CartButtonOrderProps) {
    const [isPending, startTransition] = useTransition();

    const handleOrder = async () => {
        // Validate shipping selection
        if (!shippingSelection) {
            toast.error("Vă rugăm să selectați o metodă de livrare.");
            return;
        }

        // Validate terms acceptance
        if (!acceptTerms) {
            toast.error("Vă rugăm să acceptați termenii și condițiile.");
            return;
        }

        startTransition(async () => {
              const response = await placeOrder(shippingSelection);
              if(response.success === false){
                toast.error("Nu s-a putut plasa comanda.");
              } else {
                toast.success("Comanda a fost plasată cu succes!");
                // Optionally redirect to order confirmation page
              }
        });
    }

  return (
    <>
        {isPending ? (
            <Button
              disabled
              className="w-full mt-6 bg-[#0066B1] hover:bg-[#004d85]"
            >
              <Loader2 className="w-4 h-4 animate-spin" /> 
            </Button>
          ) : (
            <Button
              onClick={handleOrder}
              className="w-full mt-6 bg-[#0066B1] dark:text-white hover:bg-[#004d85]"
            >
              Lanseaza comanda
            </Button>
          )}      
    </>
  );
}