// /lib/redis.ts

import IORedis from 'ioredis';
import { logger } from './logger';

const url = process.env.REDIS_URL;
if (!url) {
  throw new Error('REDIS_URL env var is missing');
}
const ioredisClient = new IORedis(url);

const redis = {
  async get<T>(key: string): Promise<T | null> {
    const value = await ioredisClient.get(key);
    if (value === null) {
      return null;
    }
    try {
      return JSON.parse(value) as T;
    } catch (error) {
      console.warn(`Value for key "${key}" is not valid JSON.`, value);
      logger.warn(`Value for key "${key}" is not valid JSON ${value}: ${error}`);
      return value as unknown as T;
    }
  },

  /**
   * Updated set method to support options like TTL
   */
  async set(key: string, value: unknown, options?: { ex?: number }): Promise<'OK'> {
    const stringifiedValue = typeof value === 'object' && value !== null ? JSON.stringify(value) : value;
    
    if (options?.ex) {
      // Use SETEX for setting with expiration
      return ioredisClient.setex(key, options.ex, stringifiedValue as string) as Promise<'OK'>;
    } else {
      // Regular SET without expiration
      return ioredisClient.set(key, stringifiedValue as string);
    }
  },

  async ping(): Promise<string> {
    return ioredisClient.ping();
  },
  
  async del(key: string): Promise<number> {
    return ioredisClient.del(key);
  }
};

export { redis };

// // /lib/redis.ts (or wherever you define your redis client)

// import IORedis from 'ioredis';
// import { logger } from './logger';

// // 1. Initialize the ioredis client
// const url = process.env.REDIS_URL;
// if (!url) {
//   throw new Error('REDIS_URL env var is missing');
// }
// const ioredisClient = new IORedis(url);

// // 2. Create a wrapper object that mimics the Upstash client's behavior
// const redis = {
//   /**
//    * A wrapper for the ioredis 'get' command that automatically parses JSON.
//    */
//   async get<T>(key: string): Promise<T | null> {
//     const value = await ioredisClient.get(key);
//     if (value === null) {
//       return null;
//     }
//     try {
//       // Assumes the data stored is JSON, just like @upstash/redis does
//       return JSON.parse(value) as T;
//     } catch (error) {
//       // If it's not JSON, return it as-is (though this might cause type issues)
//       // You might want to handle this case more gracefully depending on your needs.
//       console.warn(`Value for key "${key}" is not valid JSON.`, value);
//       logger.warn(`Value for key "${key}" is not valid JSON ${value}: ${error}`);
//       return value as unknown as T;
//     }
//   },

//   /**
//    * A wrapper for the ioredis 'set' command that automatically stringifies objects.
//    */
//   async set(key: string, value: unknown): Promise<'OK'> {
//     // Stringify objects/arrays, otherwise use the value as is.
//     const stringifiedValue = typeof value === 'object' && value !== null ? JSON.stringify(value) : value;
//     return ioredisClient.set(key, stringifiedValue as string);
//   },

//   async ping(): Promise<string> {
//     return ioredisClient.ping();
//   },
  
//   // You can add other wrapped commands here if your app uses them
//   // For example, a 'del' command:
//   async del(key: string): Promise<number> {
//     return ioredisClient.del(key);
//   }
// };

// // 3. Use a NAMED EXPORT to match your existing application code
// export { redis };


//for the upstash
// import { Redis } from '@upstash/redis'

// let redis: Redis | null = null

// const url = process.env.UPSTASH_REDIS_REST_URL
// const token = process.env.UPSTASH_REDIS_REST_TOKEN

// if (url && token) {
//   redis = new Redis({ url, token })
// } else {
//   console.warn('⚠️ Redis not configured. Falling back to DB-only mode.')
// }

// export { redis }

