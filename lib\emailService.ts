import nodemailer from 'nodemailer';
import { logger } from './logger';
import { OrderDataEmail } from '@/types/email';
import { ServiceEmailData } from '@/types/services';
import { formatPriceRON } from './utils';
import { formatDate } from './order-utils';

const createTransport = nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT!),
    secure: process.env.SMTP_PORT === '465', // true for 465, false for other ports like 587
    auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
    },
    // Production settings
    pool: true, // use pooled connection
    maxConnections: 5,
    maxMessages: 100,
    rateDelta: 20000, // 20 seconds
    rateLimit: 5, // max 5 emails per rateDelta
});

// Email validation function
const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Order confirmation email template
const getOrderConfirmationTemplate = (orderData: OrderDataEmail) => {
  const { orderId, customerName, items, total, shippingAddress } = orderData;
  
  return {
    subject: `✅ Comandă #${orderId} confirmată - Automobile Bavaria`,
    html: `
      <!DOCTYPE html>
      <html lang="ro">
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Confirmare Comandă</title>
          <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body { 
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
              line-height: 1.6; 
              color: #2c3e50;
              background-color: #f8f9fa;
            }
            .email-container { 
              max-width: 650px; 
              margin: 0 auto; 
              background: #ffffff;
              box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            }
            
            .header { 
              background: #0066B1;;
              color: white; 
              padding: 40px 30px;
              text-align: center;
            }
            
            .logo { 
              font-size: 28px; 
              font-weight: 700; 
              margin-bottom: 10px;
              letter-spacing: 1px;
            }
            
            .success-badge {
              display: inline-block;
              background: #27ae60;
              color: white;
              padding: 8px 20px;
              border-radius: 25px;
              font-size: 14px;
              font-weight: 600;
              margin: 20px 0 10px 0;
              box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
            }
            
            .content { padding: 40px 30px; }
            
            .order-summary {
              background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
              border-radius: 12px;
              padding: 25px;
              margin: 25px 0;
              border-left: 4px solid #667eea;
            }
            
            .order-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 20px;
              flex-wrap: wrap;
            }
            
            .order-number {
              font-size: 20px;
              font-weight: 700;
              color: #2c3e50;
            }
            
            .order-date {
              color: #7f8c8d;
              font-size: 14px;
              background: white;
              padding: 5px 12px;
              border-radius: 20px;
              border: 1px solid #e9ecef;
            }
            
            .section-title {
              font-size: 18px;
              font-weight: 600;
              color: #2c3e50;
              margin-bottom: 20px;
              padding-bottom: 10px;
              border-bottom: 2px solid #e9ecef;
            }
            
            .item {
              background: white;
              border-radius: 8px;
              padding: 20px;
              margin-bottom: 15px;
              border: 1px solid #e9ecef;
              box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            }
            
            .item-header {
              display: flex;
              justify-content: space-between;
              align-items: flex-start;
              margin-bottom: 10px;
            }
            
            .item-name {
              font-weight: 600;
              color: #2c3e50;
              font-size: 16px;
              flex: 1;
            }
            
            .item-price {
              font-weight: 700;
              color: #27ae60;
              font-size: 16px;
            }
            
            .item-details {
              display: flex;
              gap: 20px;
              color: #7f8c8d;
              font-size: 14px;
              margin-top: 8px;
            }
            
            .item-code {
              background: #f8f9fa;
              padding: 4px 8px;
              border-radius: 4px;
              font-family: 'Courier New', monospace;
              font-weight: 600;
            }
            
            .total-section {
              background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
              color: white;
              padding: 25px;
              border-radius: 12px;
              text-align: center;
              margin: 30px 0;
            }
            
            .total-label {
              font-size: 16px;
              opacity: 0.9;
              margin-bottom: 5px;
            }
            
            .total-amount {
              font-size: 32px;
              font-weight: 700;
              text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            }
            
            .shipping-section {
              background: #fff;
              border: 2px dashed #e9ecef;
              border-radius: 12px;
              padding: 25px;
              margin: 25px 0;
            }
            
            .shipping-address {
              background: #f8f9fa;
              padding: 15px;
              border-radius: 8px;
              margin-top: 15px;
              line-height: 1.8;
            }
            
            .footer {
              background: #2c3e50;
              color: white;
              padding: 30px;
              text-align: center;
            }
            
            .contact-info {
              background: rgba(255,255,255,0.1);
              padding: 20px;
              border-radius: 8px;
              margin: 20px 0;
            }
            
            .contact-email {
              color: #3498db;
              text-decoration: none;
              font-weight: 600;
            }
            
            @media (max-width: 600px) {
              .email-container { margin: 0; }
              .content, .header, .footer { padding: 20px; }
              .order-header { flex-direction: column; gap: 10px; }
              .item-header { flex-direction: column; gap: 5px; }
              .item-details { flex-direction: column; gap: 5px; }
              .total-amount { font-size: 24px; }
            }
          </style>
        </head>
        <body>
          <div class="email-container">
            <div class="header">
              <div class="logo">🚗 AUTOMOBILE BAVARIA</div>
              <div class="success-badge">✅ Comandă Confirmată</div>
              <h1>Mulțumim pentru încredere!</h1>
              <p>Comanda ta a fost înregistrată cu succes</p>
            </div>
            
            <div class="content">
              <p style="font-size: 18px; margin-bottom: 25px;">Bună ziua <strong>${customerName}</strong>,</p>
              
              <p style="margin-bottom: 25px; color: #7f8c8d;">
                Comanda ta a fost procesată cu succes! Îți mulțumim pentru încrederea acordată. 
                Mai jos găsești detaliile complete ale comenzii tale.
              </p>
              
              <div class="order-summary">
                <div class="order-header">
                  <div class="order-number">Comanda #${orderId}</div>
                  <div class="order-date">📅 ${formatDate(new Date().toISOString())}</div>
                </div>
              </div>
              
              <div style="margin: 30px 0;">
                <h3 class="section-title">🔧 Piese Comandate</h3>
                ${items.map(item => `
                  <div class="item">
                    <div class="item-header">
                      <div class="item-name">${item.name}</div>
                      <div class="item-price">${formatPriceRON(item.quantity * item.price)}</div>
                    </div>
                    <div class="item-details">
                      <div>📦 Cantitate: <strong>${item.quantity}</strong></div>
                      <div>🏷️ Preț unitar: <strong>${formatPriceRON(item.price)}</strong></div>
                      <div class="item-code">OE: ${item.code}</div>
                    </div>
                  </div>
                `).join('')}
              </div>
              
              <div class="total-section">
                <div class="total-label">Total de plată</div>
                <div class="total-amount">${formatPriceRON(total)}</div>
              </div>
              
              ${shippingAddress ? `
              <div class="shipping-section">
                <h3 class="section-title">🚚 Adresa de Livrare</h3>
                <div class="shipping-address">
                  <strong>${shippingAddress.street}</strong><br>
                  ${shippingAddress.city}, ${shippingAddress.state} ${shippingAddress.zipCode}<br>
                  ${shippingAddress.country}
                </div>
              </div>
              ` : ''}
              
              <div style="background: #e8f5e8; border-radius: 12px; padding: 25px; margin: 25px 0; border-left: 4px solid #27ae60;">
                <h3 style="color: #27ae60; margin-bottom: 15px;">📋 Următorii pași</h3>
                <ul style="color: #2c3e50; padding-left: 20px;">
                  <li style="margin-bottom: 8px;">Comanda ta este în curs de procesare</li>
                  <li style="margin-bottom: 8px;">Vei primi un email de confirmare a expedierii</li>
                  <li style="margin-bottom: 8px;">Timpul estimat de livrare: 2-5 zile lucrătoare</li>
                  <li>Poți urmări statusul comenzii în contul tău</li>
                </ul>
              </div>
            </div>
            
            <div class="footer">
              <div style="max-width: 500px; margin: 0 auto;">
                <h3 style="margin-bottom: 15px;">Ai nevoie de ajutor?</h3>
                <div class="contact-info">
                  <p>📧 Email: <a href="mailto:<EMAIL>" class="contact-email"><EMAIL></a></p>
                  <p>📞 Telefon: +40 XXX XXX XXX</p>
                  <p>🕒 Program: Luni - Vineri, 09:00 - 18:00</p>
                </div>
                
                <p style="margin-top: 20px; opacity: 0.8; font-size: 14px;">
                  © 2025 Automobile Bavaria. Toate drepturile rezervate.
                </p>
              </div>
            </div>
          </div>
        </body>
      </html>
    `,
    text: `
🚗 AUTOMOBILE BAVARIA - Confirmare Comandă

Bună ziua ${customerName},

✅ Comanda #${orderId} a fost confirmată cu succes!
📅 Data: ${formatDate(new Date().toISOString())}

🔧 PIESE COMANDATE:
${items.map(item => `
• ${item.name}
  OE: ${item.code}
  Cantitate: ${item.quantity} × ${formatPriceRON(item.price)} = ${formatPriceRON(item.quantity * item.price)}
`).join('')}

💰 TOTAL: ${formatPriceRON(total)}

${shippingAddress ? `
🚚 ADRESA DE LIVRARE:
${shippingAddress.street}
${shippingAddress.city}, ${shippingAddress.state} ${shippingAddress.zipCode}
${shippingAddress.country}
` : ''}

📋 URMĂTORII PAȘI:
• Comanda este în curs de procesare
• Vei primi email de confirmare a expedierii
• Timp estimat de livrare: 2-5 zile lucrătoare

📧 Contact: <EMAIL>

Mulțumim pentru încrederea acordată!
Automobile Bavaria
    `
  };
};

// Main email sending function
export const sendOrderConfirmationEmail = async (orderData : OrderDataEmail) => {
  try {
    if (!process.env.SMTP_HOST || !process.env.SMTP_USER || !process.env.FROM_EMAIL) {
      throw new Error('Missing required email environment variables');
    }

    if (!isValidEmail(orderData.customerEmail)) {
      throw new Error(`Invalid customer email: ${orderData.customerEmail}`);
    }

    if (!isValidEmail(process.env.FROM_EMAIL)) {
      throw new Error(`Invalid FROM_EMAIL: ${process.env.FROM_EMAIL}`);
    }
    
    // Verify connection
    await createTransport.verify();
    
    const template = getOrderConfirmationTemplate(orderData);
    
    const mailOptions = {
      from: `"${process.env.FROM_NAME}" <${process.env.FROM_EMAIL}>`,
      to: orderData.customerEmail,
      bcc: process.env.FROM_EMAIL, // Keep a copy for records
      subject: template.subject,
      html: template.html,
      text: template.text,
      // Add tracking headers
      headers: {
        'X-Order-ID': orderData.orderId,
        'X-Customer-ID': orderData.customerId,
      },
    };
    
    const result = await createTransport.sendMail(mailOptions);
    
    logger.info('Order confirmation email sent:', {
      orderId: orderData.orderId,
      messageId: result.messageId,
      to: orderData.customerEmail
    });
    
    return { success: true };
    
  } catch (error) {
    logger.error(`[sendOrderConfirmationEmail] Error sending email: ${error}`);
    
    // Don't throw error - email failure shouldn't break order processing
    return { success: false };
  }
};

// Service status email templates
const getServiceStatusTemplate = (serviceData: ServiceEmailData) => {
  const { serviceNumber, customerName, status, productName, productCode, orderNumber, resolution, resolutionNotes } = serviceData;

  const statusConfig: Record<string, {
    subject: string;
    title: string;
    message: string;
    color: string;
    icon: string;
    nextSteps: string[];
  }> = {
    requested: {
      subject: `🔧 Cerere de service #${serviceNumber} înregistrată - Automobile Bavaria`,
      title: 'Cerere de Service Înregistrată',
      message: 'Cererea ta de service a fost înregistrată cu succes!',
      color: '#3498db',
      icon: '📝',
      nextSteps: [
        'Echipa noastră va analiza cererea în cel mai scurt timp',
        'Vei fi contactat pentru programarea serviciului',
        'Poți urmări statusul în contul tău'
      ]
    },
    scheduled: {
      subject: `✅ Cerere de service #${serviceNumber} programată - Automobile Bavaria`,
      title: 'Cerere de Service Programată',
      message: 'Cererea ta de service a fost programată!',
      color: '#27ae60',
      icon: '✅',
      nextSteps: [
        'Serviciul a fost programat',
        'Vei primi detaliile de programare',
        'Pregătește produsul pentru preluare'
      ]
    },
    inProgress: {
      subject: `🔧 Service în desfășurare #${serviceNumber} - Automobile Bavaria`,
      title: 'Service în Desfășurare',
      message: 'Produsul tău este în curs de service.',
      color: '#3498db',
      icon: '🔧',
      nextSteps: [
        'Serviciul este în progres',
        'Vei fi informat despre finalizare',
        'Estimăm finalizarea în curând'
      ]
    },
    diagnosisComplete: {
      subject: `🔍 Diagnoză completă pentru service #${serviceNumber} - Automobile Bavaria`,
      title: 'Diagnoză Completă',
      message: 'Diagnoza pentru produsul tău a fost finalizată.',
      color: '#9b59b6',
      icon: '🔍',
      nextSteps: [
        'Diagnoza a fost completată',
        'Vei primi detaliile despre următorii pași',
        'Te vom informa despre costurile estimate'
      ]
    },
    awaitingParts: {
      subject: `📦 Așteptăm piese pentru service #${serviceNumber} - Automobile Bavaria`,
      title: 'Așteptăm Piese',
      message: 'Așteptăm livrarea pieselor necesare pentru service.',
      color: '#f39c12',
      icon: '📦',
      nextSteps: [
        'Piesele necesare au fost comandate',
        'Vei fi notificat când sosesc piesele',
        'Serviciul va continua după primirea pieselor'
      ]
    },
    awaitingApproval: {
      subject: `⏳ Așteptăm aprobarea pentru service #${serviceNumber} - Automobile Bavaria`,
      title: 'Așteptăm Aprobare',
      message: 'Așteptăm aprobarea ta pentru continuarea serviciului.',
      color: '#f39c12',
      icon: '⏳',
      nextSteps: [
        'Verifică detaliile serviciului',
        'Aprobă sau respinge propunerea',
        'Contactează-ne pentru întrebări'
      ]
    },
    completed: {
      subject: `🎉 Service finalizat #${serviceNumber} - Automobile Bavaria`,
      title: 'Service Finalizat',
      message: 'Serviciul a fost finalizat cu succes!',
      color: '#27ae60',
      icon: '🎉',
      nextSteps: [
        'Produsul este gata pentru ridicare/livrare',
        'Verifică detaliile de livrare',
        'Mulțumim pentru încrederea acordată!'
      ]
    },
    delivered: {
      subject: `🚚 Produs livrat pentru service #${serviceNumber} - Automobile Bavaria`,
      title: 'Produs Livrat',
      message: 'Produsul a fost livrat cu succes!',
      color: '#27ae60',
      icon: '🚚',
      nextSteps: [
        'Produsul a fost livrat',
        'Verifică produsul la primire',
        'Mulțumim pentru încrederea acordată!'
      ]
    },
    cancelled: {
      subject: `🚫 Service anulat #${serviceNumber} - Automobile Bavaria`,
      title: 'Service Anulat',
      message: 'Serviciul a fost anulat.',
      color: '#95a5a6',
      icon: '🚫',
      nextSteps: [
        'Serviciul a fost anulat conform solicitării',
        'Produsul va fi returnat dacă este cazul',
        'Poți crea o nouă cerere oricând'
      ]
    }
  };

  const config = statusConfig[status] || statusConfig.requested;

  return {
    subject: config.subject,
    html: `
      <!DOCTYPE html>
      <html lang="ro">
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Actualizare Service</title>
          <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              line-height: 1.6;
              color: #2c3e50;
              background-color: #f8f9fa;
            }
            .email-container {
              max-width: 650px;
              margin: 0 auto;
              background: #ffffff;
              box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            }

            .header {
              background: ${config.color};
              color: white;
              padding: 40px 30px;
              text-align: center;
            }

            .logo {
              font-size: 28px;
              font-weight: 700;
              margin-bottom: 10px;
              letter-spacing: 1px;
            }

            .status-badge {
              background: rgba(255,255,255,0.2);
              padding: 8px 20px;
              border-radius: 25px;
              font-size: 14px;
              font-weight: 600;
              margin: 15px 0;
              display: inline-block;
            }

            .content {
              padding: 40px 30px;
            }

            .service-summary {
              background: #f8f9fa;
              border-radius: 12px;
              padding: 25px;
              margin: 25px 0;
              border-left: 4px solid ${config.color};
            }

            .service-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 20px;
              flex-wrap: wrap;
              gap: 10px;
            }

            .service-number {
              font-size: 18px;
              font-weight: 700;
              color: ${config.color};
            }

            .service-date {
              color: #7f8c8d;
              font-size: 14px;
            }

            .product-info {
              background: white;
              border-radius: 8px;
              padding: 20px;
              margin: 15px 0;
              border: 1px solid #e9ecef;
            }

            .product-code {
              font-family: 'Courier New', monospace;
              background: #f1f3f4;
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 12px;
              color: #5f6368;
            }

            .next-steps {
              background: #e8f5e8;
              border-radius: 12px;
              padding: 25px;
              margin: 25px 0;
              border-left: 4px solid #27ae60;
            }

            .footer {
              background: #2c3e50;
              color: white;
              padding: 30px;
              text-align: center;
            }

            .contact-info p {
              margin: 8px 0;
              font-size: 14px;
            }

            .contact-email {
              color: #74b9ff;
              text-decoration: none;
            }

            @media (max-width: 600px) {
              .email-container { margin: 0; }
              .content, .header, .footer { padding: 20px; }
              .service-header { flex-direction: column; align-items: flex-start; }
            }
          </style>
        </head>
        <body>
          <div class="email-container">
            <div class="header">
              <div class="logo">🚗 AUTOMOBILE BAVARIA</div>
              <div class="status-badge">${config.icon} ${config.title}</div>
              <h1>${config.message}</h1>
            </div>

            <div class="content">
              <p style="font-size: 18px; margin-bottom: 25px;">Bună ziua <strong>${customerName}</strong>,</p>

              <div class="service-summary">
                <div class="service-header">
                  <div class="service-number">Service #${serviceNumber}</div>
                  <div class="service-date">📅 ${formatDate(new Date().toISOString())}</div>
                </div>

                <div class="product-info">
                  <h4 style="margin-bottom: 10px; color: #2c3e50;">🔧 Produs pentru service:</h4>
                  <p style="font-weight: 600; margin-bottom: 5px;">${productName}</p>
                  <p class="product-code">Cod: ${productCode}</p>
                  <p style="color: #7f8c8d; font-size: 14px; margin-top: 10px;">📦 Comandă: #${orderNumber}</p>
                </div>

                ${resolution ? `
                  <div style="background: #fff3cd; border-radius: 8px; padding: 15px; margin-top: 15px; border-left: 4px solid #ffc107;">
                    <h4 style="color: #856404; margin-bottom: 10px;">📋 Rezoluție: ${resolution}</h4>
                    ${resolutionNotes ? `<p style="color: #856404;">${resolutionNotes}</p>` : ''}
                  </div>
                ` : ''}
              </div>

              <div class="next-steps">
                <h3 style="color: #27ae60; margin-bottom: 15px;">📋 Următorii pași</h3>
                <ul style="color: #2c3e50; padding-left: 20px;">
                  ${config.nextSteps.map((step: string) => `<li style="margin-bottom: 8px;">${step}</li>`).join('')}
                </ul>
              </div>
            </div>

            <div class="footer">
              <div style="max-width: 500px; margin: 0 auto;">
                <h3 style="margin-bottom: 15px;">Ai nevoie de ajutor?</h3>
                <div class="contact-info">
                  <p>📧 Email: <a href="mailto:<EMAIL>" class="contact-email"><EMAIL></a></p>
                  <p>📞 Telefon: +40 XXX XXX XXX</p>
                  <p>🕒 Program: Luni - Vineri, 09:00 - 18:00</p>
                </div>

                <p style="margin-top: 20px; opacity: 0.8; font-size: 14px;">
                  © 2025 Automobile Bavaria. Toate drepturile rezervate.
                </p>
              </div>
            </div>
          </div>
        </body>
      </html>
    `,
    text: `
🚗 AUTOMOBILE BAVARIA - ${config.title}

Bună ziua ${customerName},

${config.icon} ${config.message}

🔧 DETALII SERVICE:
Service: #${serviceNumber}
📅 Data: ${formatDate(new Date().toISOString())}

🔧 PRODUS:
${productName}
Cod: ${productCode}
📦 Comandă: #${orderNumber}

${resolution ? `
📋 REZOLUȚIE: ${resolution}
${resolutionNotes ? resolutionNotes : ''}
` : ''}

📋 URMĂTORII PAȘI:
${config.nextSteps.map((step: string) => `• ${step}`).join('\n')}

📧 Contact: <EMAIL>

Mulțumim pentru încrederea acordată!
Automobile Bavaria
    `
  };
};

// Main service email sending function
export const sendServiceStatusEmail = async (serviceData: ServiceEmailData) => {
  try {
    if (!process.env.SMTP_HOST || !process.env.SMTP_USER || !process.env.FROM_EMAIL) {
      throw new Error('Missing required email environment variables');
    }

    if (!isValidEmail(serviceData.customerEmail)) {
      throw new Error(`Invalid customer email: ${serviceData.customerEmail}`);
    }

    if (!isValidEmail(process.env.FROM_EMAIL)) {
      throw new Error(`Invalid FROM_EMAIL: ${process.env.FROM_EMAIL}`);
    }

    // Verify connection
    await createTransport.verify();

    const template = getServiceStatusTemplate(serviceData);

    const mailOptions = {
      from: `"${process.env.FROM_NAME}" <${process.env.FROM_EMAIL}>`,
      to: serviceData.customerEmail,
      bcc: process.env.FROM_EMAIL, // Keep a copy for records
      subject: template.subject,
      html: template.html,
      text: template.text,
      // Add tracking headers
      headers: {
        'X-Service-Number': serviceData.serviceNumber,
        'X-Service-Status': serviceData.status,
        'X-Order-Number': serviceData.orderNumber,
      },
    };

    const result = await createTransport.sendMail(mailOptions);

    logger.info('Service status email sent:', {
      serviceNumber: serviceData.serviceNumber,
      status: serviceData.status,
      messageId: result.messageId,
      to: serviceData.customerEmail
    });

    return { success: true };

  } catch (error) {
    logger.error(`[sendServiceStatusEmail] Error sending email: ${error}`);

    // Don't throw error - email failure shouldn't break service processing
    return { success: false };
  }
};

// Return confirmation email template
const getReturnConfirmationTemplate = (returnData: {
  returnNumber: string;
  customerName: string;
  orderNumber: string;
  items: Array<{
    name: string;
    code: string;
    quantity: number;
    reason: string;
  }>;
}) => {
  const { returnNumber, customerName, orderNumber, items } = returnData;

  return {
    subject: `📦 Returnare #${returnNumber} confirmată - Automobile Bavaria`,
    html: `
      <!DOCTYPE html>
      <html lang="ro">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Returnare Confirmată</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }
            .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; }
            .header { background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); color: white; padding: 30px; text-align: center; }
            .content { padding: 30px; }
            .status-badge { display: inline-block; background-color: #10b981; color: white; padding: 8px 16px; border-radius: 20px; font-size: 14px; font-weight: bold; margin: 10px 0; }
            .item-card { border: 1px solid #e5e7eb; border-radius: 8px; padding: 15px; margin: 10px 0; background-color: #f9fafb; }
            .footer { background-color: #1f2937; color: white; padding: 30px; text-align: center; }
            .contact-info p { margin: 5px 0; }
            .contact-email { color: #60a5fa; text-decoration: none; }
            .next-steps { background-color: #eff6ff; border-left: 4px solid #3b82f6; padding: 15px; margin: 20px 0; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🚗 AUTOMOBILE BAVARIA</h1>
              <h2>Returnare Confirmată</h2>
              <div class="status-badge">✅ CONFIRMATĂ</div>
            </div>

            <div class="content">
              <p>Bună ziua <strong>${customerName}</strong>,</p>

              <p>Returnarea <strong>#${returnNumber}</strong> a fost confirmată cu succes!</p>

              <div class="next-steps">
                <h3>📋 Detalii Returnare:</h3>
                <p><strong>Număr returnare:</strong> #${returnNumber}</p>
                <p><strong>Comandă originală:</strong> #${orderNumber}</p>
                <p><strong>Data:</strong> ${formatDate(new Date().toISOString())}</p>
              </div>

              <h3>📦 Produse pentru returnare:</h3>
              ${items.map(item => `
                <div class="item-card">
                  <h4>${item.name}</h4>
                  <p><strong>Cod:</strong> ${item.code}</p>
                  <p><strong>Cantitate:</strong> ${item.quantity}</p>
                  <p><strong>Motiv:</strong> ${item.reason}</p>
                </div>
              `).join('')}

              <div class="next-steps">
                <h3>📋 Următorii pași:</h3>
                <ul>
                  <li>Vei fi contactat în maxim 24 de ore pentru detalii</li>
                  <li>Pregătește produsele pentru ridicare/returnare</li>
                  <li>Păstrează ambalajul original dacă este posibil</li>
                  <li>Poți urmări statusul în secțiunea "Returnări"</li>
                </ul>
              </div>
            </div>

            <div class="footer">
              <div style="max-width: 500px; margin: 0 auto;">
                <h3 style="margin-bottom: 15px;">Ai nevoie de ajutor?</h3>
                <div class="contact-info">
                  <p>📧 Email: <a href="mailto:<EMAIL>" class="contact-email"><EMAIL></a></p>
                  <p>📞 Telefon: +40 XXX XXX XXX</p>
                  <p>🕒 Program: Luni - Vineri, 09:00 - 18:00</p>
                </div>

                <p style="margin-top: 20px; opacity: 0.8; font-size: 14px;">
                  © 2025 Automobile Bavaria. Toate drepturile rezervate.
                </p>
              </div>
            </div>
          </div>
        </body>
      </html>
    `,
    text: `
🚗 AUTOMOBILE BAVARIA - Returnare Confirmată

Bună ziua ${customerName},

📦 Returnarea #${returnNumber} a fost confirmată cu succes!
📅 Data: ${formatDate(new Date().toISOString())}

📋 DETALII RETURNARE:
Returnare: #${returnNumber}
Comandă originală: #${orderNumber}

📦 PRODUSE PENTRU RETURNARE:
${items.map(item => `
• ${item.name}
  Cod: ${item.code}
  Cantitate: ${item.quantity}
  Motiv: ${item.reason}
`).join('')}

📋 URMĂTORII PAȘI:
• Vei fi contactat în maxim 24 de ore pentru detalii
• Pregătește produsele pentru ridicare/returnare
• Păstrează ambalajul original dacă este posibil
• Poți urmări statusul în secțiunea "Returnări"

📧 Contact: <EMAIL>

Mulțumim pentru încrederea acordată!
Automobile Bavaria
    `
  };
};

// Return email sending function
export const sendReturnConfirmationEmail = async (returnData: {
  returnNumber: string;
  customerName: string;
  customerEmail: string;
  orderNumber: string;
  items: Array<{
    name: string;
    code: string;
    quantity: number;
    reason: string;
  }>;
}) => {
  try {
    if (!process.env.SMTP_HOST || !process.env.SMTP_USER || !process.env.FROM_EMAIL) {
      throw new Error('Missing required email environment variables');
    }

    if (!isValidEmail(returnData.customerEmail)) {
      throw new Error(`Invalid customer email: ${returnData.customerEmail}`);
    }

    if (!isValidEmail(process.env.FROM_EMAIL)) {
      throw new Error(`Invalid FROM_EMAIL: ${process.env.FROM_EMAIL}`);
    }

    // Verify connection
    await createTransport.verify();

    const template = getReturnConfirmationTemplate(returnData);

    const mailOptions = {
      from: `"${process.env.FROM_NAME}" <${process.env.FROM_EMAIL}>`,
      to: returnData.customerEmail,
      bcc: process.env.FROM_EMAIL, // Keep a copy for records
      subject: template.subject,
      html: template.html,
      text: template.text,
      // Add tracking headers
      headers: {
        'X-Return-Number': returnData.returnNumber,
        'X-Order-Number': returnData.orderNumber,
      },
    };

    const result = await createTransport.sendMail(mailOptions);

    logger.info('Return confirmation email sent:', {
      returnNumber: returnData.returnNumber,
      messageId: result.messageId,
      to: returnData.customerEmail
    });

    return { success: true };

  } catch (error) {
    logger.error(`[sendReturnConfirmationEmail] Error sending email: ${error}`);

    // Don't throw error - email failure shouldn't break return processing
    return { success: false };
  }
};
