// components/hero-banner-skeleton.jsx
import { Skeleton } from "@/components/ui/skeleton"

export function HeroSkeleton() {
  return (
    <div className="relative w-full h-[600px] overflow-hidden bg-gray-900">
      {/* Background and Text Skeleton */}
      <Skeleton className="absolute inset-0 w-full h-full" />

      {/* Content Skeleton */}
      <div className="relative h-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex flex-col justify-center">
        <Skeleton className="h-12 w-3/4 mb-4" />
        <Skeleton className="h-8 w-2/3" />
      </div>

      {/* Slide indicators skeleton */}
      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2">
        <Skeleton className="h-2 w-8 rounded-full" />
        <Skeleton className="h-2 w-2 rounded-full" />
        <Skeleton className="h-2 w-2 rounded-full" />
      </div>
    </div>
  );
}