"use client"

import { useState, useTransition, useMemo, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChevronLeft,
  ChevronRight,
  Package,
  Truck,
  Plus,
  Minus,
  ShoppingCart,
} from "lucide-react";
import { OrderItemForReturn, ReturnItemReason } from "@/types/returns";
import { formatDate } from "@/lib/order-utils";
import Image from "next/image";
import { createReturn } from "@/app/actions/returns";
import { toast } from "sonner";
import { ShippingAddress, ShippingSelection, ShowroomData } from "@/types/addresses";
import AddressSelector from "@/app/components/shared/AddressSelector";

// Return reason labels (per product)
const RETURN_REASON_LABELS = {
  wrongItem: "Produs greșit",
  defective: "Produs defect",
  damaged: "Produs deteriorat",
  notAsDescribed: "Nu corespunde descrierii",
  noLongerWanted: "Nu mai doresc produsul",
  other: "Altul",
};

// Group products by order
interface OrderGroup {
  orderId: string;
  orderNumber: string;
  orderDate: string;
  items: OrderItemForReturn[];
}

// Selected item for return
interface SelectedReturnItem {
  orderItemId: string;
  quantity: number;
  reason: ReturnItemReason;
  description: string;
}

interface NewReturnDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  orderItems: OrderItemForReturn[];
  shippingAddresses: ShippingAddress[];
  showrooms?: ShowroomData[];
  onReturnCreated?: () => void;
}

export default function NewReturnDialog({
  open,
  onOpenChange,
  orderItems,
  shippingAddresses,
  showrooms = [],
  onReturnCreated,
}: NewReturnDialogProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [isPending, startTransition] = useTransition();
  const [selectedOrderId, setSelectedOrderId] = useState<string>("");
  const [selectedItems, setSelectedItems] = useState<SelectedReturnItem[]>([]);
  const [shippingSelection, setShippingSelection] = useState<ShippingSelection | undefined>();

  // Group items by order
  const orderGroups = useMemo((): OrderGroup[] => {
    const groups = new Map<string, OrderGroup>();

    orderItems.forEach(item => {
      if (!groups.has(item.order.id)) {
        groups.set(item.order.id, {
          orderId: item.order.id,
          orderNumber: item.order.orderNumber,
          orderDate: item.order.placedAt,
          items: []
        });
      }
      groups.get(item.order.id)!.items.push(item);
    });

    return Array.from(groups.values()).sort((a, b) =>
      new Date(b.orderDate).getTime() - new Date(a.orderDate).getTime()
    );
  }, [orderItems]);

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (!open) {
      setCurrentStep(1);
      setSelectedOrderId("");
      setSelectedItems([]);
      setShippingSelection(undefined);
    }
  }, [open]);

  const steps = [
    { number: 1, title: "Selectează comanda", icon: ShoppingCart },
    { number: 2, title: "Selectează produsele", icon: Package },
    { number: 3, title: "Adresa de ridicare", icon: Truck },
  ];

  // Get selected order items
  const selectedOrderItems = useMemo(() => {
    if (!selectedOrderId) return [];
    const orderGroup = orderGroups.find(group => group.orderId === selectedOrderId);
    return orderGroup?.items || [];
  }, [selectedOrderId, orderGroups]);

  const handleItemSelection = (orderItemId: string, checked: boolean) => {
    if (checked) {
      const orderItem = selectedOrderItems.find(item => item.id === orderItemId);
      if (orderItem) {
        setSelectedItems(prev => [...prev, {
          orderItemId,
          quantity: 1,
          reason: "defective",
          description: "",
        }]);
      }
    } else {
      setSelectedItems(prev => prev.filter(item => item.orderItemId !== orderItemId));
    }
  };

  const updateSelectedItem = (orderItemId: string, updates: Partial<SelectedReturnItem>) => {
    setSelectedItems(prev => prev.map(item =>
      item.orderItemId === orderItemId ? { ...item, ...updates } : item
    ));
  };

  const handleQuantityChange = (orderItemId: string, delta: number) => {
    const orderItem = selectedOrderItems.find(item => item.id === orderItemId);
    if (!orderItem) return;

    setSelectedItems(prev => prev.map(item => {
      if (item.orderItemId === orderItemId) {
        const newQuantity = Math.max(1, Math.min(orderItem.availableQuantity, item.quantity + delta));
        return { ...item, quantity: newQuantity };
      }
      return item;
    }));
  };

  // Validation for each step
  const canProceedToStep2 = selectedOrderId !== "";
  const canProceedToStep3 = selectedItems.length > 0 && selectedItems.every(item => item.reason && item.description.trim());
  const canSubmit = shippingSelection && (
    shippingSelection.method === 'intern' ||
    (shippingSelection.method === 'showroom' && shippingSelection.showroomId) ||
    (shippingSelection.method === 'curier' && shippingSelection.addressId)
  );

  const handleSubmit = async () => {
    if (!canSubmit || selectedItems.length === 0 || !selectedOrderId) {
      toast.error("Vă rugăm să completați toate câmpurile obligatorii");
      return;
    }

    startTransition(async () => {
      try {
        if (!shippingSelection) {
          toast.error("Vă rugăm să selectați o metodă de livrare");
          return;
        }

        // Validate shipping selection
        let addressId: string | undefined;
        let showroomId: string | undefined;

        if (shippingSelection.method === 'curier') {
          if (!shippingSelection.addressId) {
            toast.error("Vă rugăm să selectați o adresă pentru livrare prin curier");
            return;
          }
          addressId = shippingSelection.addressId;
        } else if (shippingSelection.method === 'showroom') {
          if (!shippingSelection.showroomId) {
            toast.error("Vă rugăm să selectați un showroom pentru ridicare");
            return;
          }
          showroomId = shippingSelection.showroomId;
        } else if (shippingSelection.method === 'intern') {
          // Internal method doesn't need additional data
        } else {
          toast.error("Metoda de livrare selectată nu este validă");
          return;
        }

        // Create return for the selected order
        const result = await createReturn({
          orderId: selectedOrderId,
          method: shippingSelection.method,
          addressId,
          showroomId,
          items: selectedItems,
        });

        if (result.success) {
          toast.success(`Returnarea ${result.returnNumber} a fost creată cu succes`);
          onReturnCreated?.();
          onOpenChange(false);
        } else {
          toast.error("A apărut o eroare la crearea returnării");
        }
      } catch {
        toast.error("A apărut o eroare neașteptată la crearea returnării");
      }
    });
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-4">
            <div className="text-center space-y-2">
              <h3 className="text-lg font-semibold">Selectează comanda</h3>
              <p className="text-sm text-muted-foreground">
                Alege comanda din care dorești să returnezi produse
              </p>
            </div>

            <div className="space-y-3">
              {orderGroups.length === 0 ? (
                <div className="text-center py-8">
                  <Package className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">Nu există comenzi eligibile pentru returnare</p>
                </div>
              ) : (
                orderGroups.map((orderGroup) => (
                  <Card
                    key={orderGroup.orderId}
                    className={`cursor-pointer transition-colors ${
                      selectedOrderId === orderGroup.orderId
                        ? 'ring-2 ring-primary bg-primary/5'
                        : 'hover:bg-muted/50'
                    }`}
                    onClick={() => setSelectedOrderId(orderGroup.orderId)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                          <Checkbox
                            checked={selectedOrderId === orderGroup.orderId}
                            onChange={() => {}} // Handled by card click
                          />
                        </div>
                        <div>
                          <CardTitle className="text-base">Comanda #{orderGroup.orderNumber}</CardTitle>
                          <CardDescription>
                            {formatDate(orderGroup.orderDate)} • {orderGroup.items.length} produse
                          </CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="space-y-3">
                        {orderGroup.items.slice(0, 3).map((item) => (
                          <div key={item.id} className="flex items-center gap-3 p-3 bg-muted/20 rounded-lg border border-muted/40">
                            <div className="flex-1">
                              <p className="text-sm font-medium leading-tight mb-1">
                                {item.product.Description_Local || 'Produs necunoscut'}
                              </p>
                              <div className="flex items-center justify-between">
                                <p className="text-xs text-muted-foreground font-mono">
                                  {item.product.Material_Number}
                                </p>
                                <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                                  Qty: {item.quantity}
                                </span>
                              </div>
                            </div>
                          </div>
                        ))}
                        {orderGroup.items.length > 3 && (
                          <div className="text-xs text-muted-foreground text-center py-2 bg-muted/10 rounded-md">
                            +{orderGroup.items.length - 3} produse în plus
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-4">
            <div className="text-center space-y-2">
              <h3 className="text-lg font-semibold">Selectează produsele</h3>
              <p className="text-sm text-muted-foreground">
                Alege produsele pe care dorești să le returnezi din comanda selectată
              </p>
            </div>

            {selectedOrderItems.length === 0 ? (
              <div className="text-center py-8 space-y-4">
                <Package className="h-12 w-12 text-muted-foreground mx-auto" />
                <div className="space-y-2">
                  <h3 className="text-lg font-semibold">Nu sunt produse disponibile</h3>
                  <p className="text-muted-foreground">
                    Nu există produse disponibile pentru returnare în această comandă.
                  </p>
                </div>
              </div>
            ) : (
              <div className="max-h-96 overflow-y-auto space-y-3">
                {selectedOrderItems.map((orderItem) => {
                const isSelected = selectedItems.some(item => item.orderItemId === orderItem.id);
                const selectedItem = selectedItems.find(item => item.orderItemId === orderItem.id);

                return (
                  <div key={orderItem.id} className="border rounded-lg p-4">
                    <div className="flex items-start gap-4">
                      <Checkbox
                        checked={isSelected}
                        onCheckedChange={(checked) => handleItemSelection(orderItem.id, !!checked)}
                      />

                      <div className="relative h-16 w-16 flex-shrink-0">
                        <Image
                          src={orderItem.product.ImageUrl[0] || "/placeholder-product.jpg"}
                          alt={orderItem.product.Description_Local}
                          fill
                          className="object-cover rounded-md"
                        />
                      </div>

                      <div className="flex-1 space-y-2">
                        <div>
                          <h4 className="font-medium text-sm">
                            {orderItem.product.Description_Local}
                          </h4>
                          <h5 className="font-medium text-sm">
                            {orderItem.product.Material_Number}
                          </h5>
                          <p className="text-xs text-muted-foreground">
                            Order #{orderItem.order.orderNumber}
                          </p>
                          <div className="flex items-center gap-4 text-xs text-muted-foreground mt-1">
                            <span>Quantity: {orderItem.quantity}</span>
                            <span>Ordered {formatDate(orderItem.order.placedAt)}</span>
                          </div>
                        </div>

                        {isSelected && selectedItem && (
                          <div className="space-y-3 pt-3 border-t">
                            <div className="flex items-center gap-2">
                              <span className="text-sm">Cantitate returnată:</span>
                              <div className="flex items-center gap-1">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleQuantityChange(orderItem.id, -1)}
                                  disabled={selectedItem.quantity <= 1}
                                >
                                  <Minus className="h-3 w-3" />
                                </Button>
                                <span className="w-8 text-center text-sm">{selectedItem.quantity}</span>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => handleQuantityChange(orderItem.id, 1)}
                                  disabled={selectedItem.quantity >= orderItem.availableQuantity}
                                >
                                  <Plus className="h-3 w-3" />
                                </Button>
                              </div>
                              <span className="text-xs text-muted-foreground">
                                (max {orderItem.availableQuantity})
                              </span>
                            </div>

                            <div className="space-y-2">
                              <Label className="text-sm">Motiv returnare *</Label>
                              <Select
                                value={selectedItem.reason}
                                onValueChange={(value) => updateSelectedItem(orderItem.id, { reason: value as ReturnItemReason })}
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Selectează motivul" />
                                </SelectTrigger>
                                <SelectContent>
                                  {Object.entries(RETURN_REASON_LABELS).map(([value, label]) => (
                                    <SelectItem key={value} value={value}>{label}</SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>

                            <div className="space-y-2">
                              <Label className="text-sm">Descriere problemă *</Label>
                              <Textarea
                                placeholder="Descrie problema cu produsul..."
                                value={selectedItem.description}
                                onChange={(e) => updateSelectedItem(orderItem.id, { description: e.target.value })}
                                rows={2}
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
              </div>
            )}
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center space-y-2">
              <h3 className="text-lg font-semibold">Adresa de ridicare</h3>
              <p className="text-sm text-muted-foreground">
                Selectează adresa de unde să ridicăm produsele returnate
              </p>
            </div>

            <AddressSelector
              addresses={shippingAddresses}
              showrooms={showrooms}
              value={shippingSelection}
              onValueChange={setShippingSelection}
            />
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-xl">
                Returnare nouă
              </DialogTitle>
              <DialogDescription>
                Urmează pașii pentru a crea o nouă returnare
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        {/* Step indicator */}
        <div className="flex items-center justify-center space-x-8 py-4">
          {steps.map((step, index) => (
            <div key={step.number} className="flex items-center">
              <div className={`flex items-center space-x-2 ${
                currentStep === step.number ? 'text-primary' : 
                currentStep > step.number ? 'text-green-600' : 'text-muted-foreground'
              }`}>
                <div className={`flex h-8 w-8 items-center justify-center rounded-full border-2 ${
                  currentStep === step.number ? 'border-primary bg-primary text-primary-foreground' :
                  currentStep > step.number ? 'border-green-600 bg-green-600 text-white' :
                  'border-muted-foreground'
                }`}>
                  <step.icon className="h-4 w-4" />
                </div>
                <span className="text-sm font-medium">{step.title}</span>
              </div>
              {index < steps.length - 1 && (
                <div className={`mx-4 h-px w-12 ${
                  currentStep > step.number ? 'bg-green-600' : 'bg-muted-foreground'
                }`} />
              )}
            </div>
          ))}
        </div>

        <Separator />

        {/* Step content */}
        <div className="py-6">
          {renderStepContent()}
        </div>

        {/* Navigation */}
        <div className="flex justify-between pt-4 border-t">
          <Button
            variant="outline"
            onClick={() => setCurrentStep(prev => Math.max(1, prev - 1))}
            disabled={currentStep === 1}
            className="flex items-center gap-2"
          >
            <ChevronLeft className="h-4 w-4" />
            Inapoi
          </Button>

          {currentStep < 3 ? (
            <Button
              onClick={() => setCurrentStep(prev => prev + 1)}
              disabled={
                (currentStep === 1 && !canProceedToStep2) ||
                (currentStep === 2 && !canProceedToStep3)
              }
              className="flex items-center gap-2"
            >
              Următor
              <ChevronRight className="h-4 w-4" />
            </Button>
          ) : (
            <Button
              onClick={handleSubmit}
              disabled={!canSubmit || isPending}
              className="flex items-center gap-2"
            >
              {isPending ? "Se trimite..." : "Trimite returnarea"}
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
