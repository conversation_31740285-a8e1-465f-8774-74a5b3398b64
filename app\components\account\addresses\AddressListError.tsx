import { But<PERSON> } from "@/components/ui/button";
import { AlertCircle, RefreshCw } from "lucide-react";

interface AddressListErrorProps {
  title: string;
  message?: string;
  onRetry?: () => void;
}

export default function AddressListError({ 
  title, 
  message = "A apărut o eroare la încărcarea adreselor. Vă rugăm să încercați din nou.",
  onRetry 
}: AddressListErrorProps) {
  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      <div className="flex justify-between items-start mb-8">
        <div className="space-y-2">
          <h1 className="text-2xl font-semibold">{title}</h1>
          <p className="text-muted-foreground">
            Gestionați adresele pentru comenzile dumneavoastră
          </p>
        </div>
      </div>

      <div className="flex flex-col items-center justify-center rounded-lg border border-dashed text-center py-16">
        <div className="flex h-20 w-20 items-center justify-center rounded-full bg-destructive/10 mb-6">
          <AlertCircle className="w-10 h-10 text-destructive" />
        </div>
        
        <h2 className="text-xl font-semibold mb-2">Eroare la încărcarea adreselor</h2>
        
        <p className="text-muted-foreground mb-6 max-w-md mx-auto">
          {message}
        </p>

        {onRetry && (
          <Button onClick={onRetry} className="flex items-center gap-2">
            <RefreshCw className="h-4 w-4" />
            Încearcă din nou
          </Button>
        )}
      </div>
    </div>
  );
}
