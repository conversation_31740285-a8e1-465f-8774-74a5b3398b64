"use client";

import { useTransition } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  MoreVertical, 
  Edit, 
  Trash2, 
  Star, 
  StarOff,
  MapPin,
  Building,
  Phone,
  CreditCard,
  Banknote
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { BillingAddress, ShippingAddress, AddressType } from "@/types/addresses";
import {
  setDefaultBillingAddress,
  setDefaultShippingAddress
} from "@/app/actions/addresses";
import { toast } from "sonner";

interface AddressListItemProps {
  address: BillingAddress | ShippingAddress;
  type: AddressType;
  onEdit: (address: BillingAddress | ShippingAddress) => void;
  onDelete: (address: BillingAddress | ShippingAddress) => void;
}

export default function AddressListItem({ address, type, onEdit, onDelete }: AddressListItemProps) {
  const [isPending, startTransition] = useTransition();

  const handleSetDefault = () => {
    startTransition(async () => {
      const result = type === 'billing' 
        ? await setDefaultBillingAddress(address.id)
        : await setDefaultShippingAddress(address.id);

      if (result.success) {
        toast.success(
          type === 'billing' 
            ? 'Adresa de facturare implicită a fost setată'
            : 'Adresa de livrare implicită a fost setată'
        );
      } else {
        toast.error(result.error || 'A apărut o eroare');
      }
    });
  };

  const handleDelete = () => {
    onDelete(address);
  };

  const isBillingAddress = (addr: BillingAddress | ShippingAddress): addr is BillingAddress => {
    return type === 'billing';
  };

  const isShippingAddress = (addr: BillingAddress | ShippingAddress): addr is ShippingAddress => {
    return type === 'shipping';
  };

  return (
    <div className="flex items-center gap-6 p-6 border-b last:border-0">
      {/* Icon */}
      <div className="w-12 h-12 rounded-lg bg-muted/50 flex items-center justify-center flex-shrink-0">
        {type === 'billing' ? (
          <CreditCard className="h-6 w-6 text-muted-foreground" />
        ) : (
          <MapPin className="h-6 w-6 text-muted-foreground" />
        )}
      </div>

      {/* Address Details */}
      <div className="flex-1 space-y-2">
        <div className="flex items-center gap-2">
          <h3 className="text-lg font-semibold">{address.fullName}</h3>
          {address.isDefault && (
            <Badge variant="default" className="flex items-center gap-1">
              <Star className="h-3 w-3" />
              Implicită
            </Badge>
          )}
        </div>

        <div className="space-y-1">
          <div className="flex items-start gap-2">
            <MapPin className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
            <div className="text-sm text-muted-foreground">
              <p>{address.address}</p>
              <p>{address.city}, {address.county}</p>
            </div>
          </div>

          {isBillingAddress(address) && address.companyName && (
            <div className="flex items-center gap-2">
              <Building className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">{address.companyName}</span>
            </div>
          )}

          {isBillingAddress(address) && address.cui && (
            <div className="flex items-center gap-2">
              <CreditCard className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">CUI: {address.cui}</span>
            </div>
          )}

          {isBillingAddress(address) && address.iban && (
            <div className="flex items-center gap-2">
              <Banknote className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">IBAN: {address.iban}</span>
            </div>
          )}

          {isShippingAddress(address) && (
            <div className="flex items-center gap-2">
              <Phone className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">{address.phoneNumber}</span>
            </div>
          )}

          {isShippingAddress(address) && address.notes && (
            <div className="text-sm text-muted-foreground italic">
              Note: {address.notes}
            </div>
          )}
        </div>
      </div>

      {/* Actions */}
      <div className="flex items-center gap-2">
        {!address.isDefault && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleSetDefault}
            disabled={isPending}
            className="flex items-center gap-1"
          >
            <StarOff className="h-4 w-4" />
            Setează ca implicită
          </Button>
        )}

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => onEdit(address)}>
              <Edit className="h-4 w-4 mr-2" />
              Editează
            </DropdownMenuItem>
            {!address.isDefault && (
              <>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleSetDefault} disabled={isPending}>
                  <Star className="h-4 w-4 mr-2" />
                  Setează ca implicită
                </DropdownMenuItem>
              </>
            )}
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              onClick={handleDelete}
              className="text-destructive focus:text-destructive"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Șterge
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
