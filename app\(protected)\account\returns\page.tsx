"server-only"

import { getCurrentDbUser } from "@/lib/auth";
import { getUserReturns, getReturnStatistics, getReturnableOrderItems } from "@/app/getData/returns";
import { getUserShippingAddresses, getActiveShowrooms } from "@/app/getData/addresses";
import { redirect } from "next/navigation";
import { Package } from "lucide-react";
import { logger } from "@/lib/logger";
import { returnFiltersSchema } from "@/lib/zod";
import { ReturnFilters } from "@/types/returns";
import ReturnsPage from "@/app/components/account/returns/ReturnsPage";

interface ReturnsRouteProps {
  searchParams: Promise<{
    page?: string;
    status?: string;
    search?: string;
    limit?: string;
    dateFrom?: string;
    dateTo?: string;
  }>;
}

export default async function ReturnsRoute({ searchParams }: ReturnsRouteProps) {
  // Get current user
  const user = await getCurrentDbUser();
  if (!user) {
    return redirect("/sign-in");
  }

  const params = await searchParams;
  const validationResult = returnFiltersSchema.safeParse(params);

  if (!validationResult.success) {
    return redirect("/account/returns"); // Redirect to a clean state
  }

  const validatedFilters: ReturnFilters = validationResult.data;

  try {
    // Fetch returns data, statistics, addresses, showrooms, and returnable items in parallel
    const [returnsData, statistics, shippingAddresses, showrooms, returnableItems] = await Promise.all([
      getUserReturns(user.id, validatedFilters),
      getReturnStatistics(user.id),
      getUserShippingAddresses(user.id),
      getActiveShowrooms(),
      getReturnableOrderItems(user.id),
    ]);

    return (
      <ReturnsPage
        initialReturns={returnsData.returns}
        pagination={returnsData.pagination}
        filters={validatedFilters}
        statistics={statistics}
        shippingAddresses={shippingAddresses}
        showrooms={showrooms}
        returnableItems={returnableItems}
      />
    );

  } catch (error) {
    logger.error(`[ReturnsRoute] Error loading returns for user ${user.id}:`, error);

    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <Package className="h-12 w-12 text-muted-foreground" />
        <div className="text-center space-y-2">
          <h3 className="text-lg font-semibold">Eroare la încărcarea returnărilor</h3>
          <p className="text-muted-foreground">
            A apărut o eroare la încărcarea returnărilor. Vă rugăm să încercați din nou.
          </p>
        </div>
      </div>
    );
  }
}