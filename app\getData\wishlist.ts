"server-only"

import { prisma, withRetry } from "@/lib/db"
import { logger } from "@/lib/logger"
import { toSafeNumber } from "@/lib/utils"
import { cuidSchema, productCodSchema } from "@/lib/zod"
import { WishlistItems } from "@/types/wishlist"


// Get wishlist items for the current user on the Wishlist route
export async function getWishlistItems(userIdDb: string): Promise<WishlistItems[]>{
  if (!userIdDb){
    logger.warn(`[getWishlistItems] No paramater provided`)
    return []
  }

  try{
    const userIdParsed = cuidSchema.safeParse(userIdDb)

    if (!userIdParsed.success) {
      logger.error("Invalid user ID provided:", userIdParsed.error.format());
      return []; // Return empty array for invalid IDs
    }

    const userId = userIdParsed.data
    
    const wishlistItems = await withRetry(() => prisma.wishlist.findMany({
        where: {
          userId,
        },
        select: {
          product: {
            select: {
              ImageUrl: true,
              Material_Number: true,
              Description_Local: true,
              FinalPrice: true,
              PretAM: true,
              HasDiscount: true,
              activeDiscountType: true,
              activeDiscountValue: true,
              discountPercentage: true,
            }
          }, 
        },
        orderBy: {
          createdAt: "desc", 
        },
      })
    )
       
    const safeWishlistItems = wishlistItems.map(item => ({
      product: {
        ImageUrl: item.product.ImageUrl,
        Material_Number: item.product.Material_Number,
        Description_Local: item.product.Description_Local,
        FinalPrice: toSafeNumber(item.product.FinalPrice),
        PretAM: toSafeNumber(item.product.PretAM),
        HasDiscount: item.product.HasDiscount,
        activeDiscountType: item.product.activeDiscountType,
        activeDiscountValue: toSafeNumber(item.product.activeDiscountValue),
        discountPercentage: toSafeNumber(item.product.discountPercentage),
      },
      }))

    return safeWishlistItems
  }catch(e){
    logger.error(`[getWishlistItems] Error trying to get the wishlist for userId ${userIdDb}: ${e}`)
    return []
  }
}

export async function isProductInWishlist(userIdDb: string, productCodeRaw: string): Promise<boolean> {
  if (!userIdDb || !productCodeRaw) {
    logger.warn("[isProductInWishlist] Missing userId or productCode");
    return false;
  }

  const userIdParsed = cuidSchema.safeParse(userIdDb);
  const productCodeParsed = productCodSchema.safeParse(productCodeRaw);

  if (!userIdParsed.success || !productCodeParsed.success) {
    logger.error("[isProductInWishlist] Invalid inputs", {
      productCodeError: productCodeParsed.error?.format?.(),
    });
    return false;
  }

  const userId = userIdParsed.data;
  const productCode = productCodeParsed.data;

  try {
    const existingItem = await withRetry(() =>
      prisma.wishlist.findUnique({
        where: {
          userId_productCode: {
            userId,
            productCode,
          },
        },
        select: { id: true }, // only fetch what you need
      })
    );

    return !!existingItem;
  } catch (e) {
    logger.error(`[isProductInWishlist] Error for userId ${userId} and productCode ${productCode}:`, e);
    return false;
  }
}

//used in landing page
export async function getWishlistProductCodes(userId: string): Promise<Set<string>> {
  const userIdParsed = cuidSchema.safeParse(userId);

  if (!userIdParsed.success) {
    logger.error("[getWishlistProductCodes] Invalid userId", userIdParsed.error.format());
    return new Set();
  }

  try {
    const wishlist = await withRetry(() =>
      prisma.wishlist.findMany({
        where: { userId: userIdParsed.data },
        select: { productCode: true },
      })
    );

    return new Set(wishlist.map((w) => w.productCode));
  } catch (e) {
    logger.error(`[getWishlistProductCodes] DB error for user ${userId}:`, e);
    return new Set();
  }
}