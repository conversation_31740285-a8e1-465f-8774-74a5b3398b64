"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Package, MapPin, FileText, AlertCircle } from "lucide-react";
import { useTransition, useState } from "react";
import { useForm } from "react-hook-form";
import { createServiceRequestSchema, type CreateServiceRequestInput } from "@/lib/zod";
import { createServiceRequest } from "@/app/actions/services";
import { toast } from "sonner";
import { IssueType, ProductForService } from "@/types/services";
import { ShippingAddress, ShippingSelection, ShowroomData } from "@/types/addresses";
import AddressSelector from "@/app/components/shared/AddressSelector";
import Image from "next/image";

// Mock addresses - in real implementation, these would also come from props or API calls
// TODO: Implement getAddressesForService function similar to getProductsForService

// Mock addresses removed - now using real data from props

interface NewServiceRequestDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  availableProducts: ProductForService[];
  availableAddresses: ShippingAddress[];
  showrooms: ShowroomData[];
  onServiceRequestCreated?: () => void;
}

export default function NewServiceRequestDialog({
  open,
  onOpenChange,
  availableProducts,
  availableAddresses,
  showrooms,
  onServiceRequestCreated,
}: NewServiceRequestDialogProps) {
  const [isPending, startTransition] = useTransition();
  const [shippingSelection, setShippingSelection] = useState<ShippingSelection | undefined>();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
  } = useForm<CreateServiceRequestInput>({
    // Don't use zodResolver here since we validate manually with shipping data
    mode: 'onSubmit',
  });

  // No need to watch orderItemId anymore since we removed the orderId dependency

  const onSubmit = (data: CreateServiceRequestInput) => {

    // Manual validation for required fields
    if (!data.orderItemId) {
      toast.error('Vă rugăm să selectați un produs');
      return;
    }

    if (!data.issueType) {
      toast.error('Vă rugăm să selectați tipul problemei');
      return;
    }

    if (!data.description || data.description.trim().length < 10) {
      toast.error('Descrierea trebuie să aibă cel puțin 10 caractere');
      return;
    }

    if (!shippingSelection) {
      toast.error('Vă rugăm să selectați o metodă de livrare');
      return;
    }

    // Prepare the data with shipping information
    const serviceData: CreateServiceRequestInput = {
      orderItemId: data.orderItemId,
      issueType: data.issueType,
      description: data.description.trim(),
      method: shippingSelection.method,
      addressId: shippingSelection.addressId,
      showroomId: shippingSelection.showroomId,
    };

    // Validate the complete data with Zod before submitting
    const validation = createServiceRequestSchema.safeParse(serviceData);
    if (!validation.success) {
      console.error('Validation errors:', validation.error.errors);
      const errorMessages = validation.error.errors.map(err => err.message).join(', ');
      toast.error(`Date invalide: ${errorMessages}`);
      return;
    }

    startTransition(async () => {
      const result = await createServiceRequest(serviceData);
      if (result.success) {
        toast.success(`Cererea de service ${result.serviceNumber} a fost creată cu succes!`);
        reset();
        setShippingSelection(undefined);
        onOpenChange(false);
        onServiceRequestCreated?.();
      } else {
        console.error('Service request creation failed:', result.error);
        toast.error(result.error || 'Eroare la crearea cererii de service');
      }
    });
  };

  const handleClose = () => {
    reset();
    setShippingSelection(undefined);
    onOpenChange(false);
  };

  const getIssueTypeLabel = (issueType: IssueType) => {
    switch (issueType) {
      case 'DEFECTIVE_PART':
        return "Piesă defectă";
      case 'DAMAGED_IN_SHIPPING':
        return "Deteriorat în transport";
      case 'WRONG_ITEM_RECEIVED':
        return "Produs greșit primit";
      case 'MISSING_PARTS':
        return "Piese lipsă";
      case 'NOT_AS_DESCRIBED':
        return "Nu corespunde descrierii";
      case 'COMPATIBILITY_ISSUE':
        return "Problemă de compatibilitate";
      case 'OTHER':
        return "Altele";
      default:
        return issueType;
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Cerere Nouă de Service</DialogTitle>
          <DialogDescription>
            Completează formularul pentru a crea o cerere de service pentru produsele achiziționate.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Product Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Package className="h-5 w-5" />
                Selectează Produsul
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="orderItemId">Produs pentru service *</Label>
                {availableProducts.length === 0 ? (
                  <div className="p-6 text-center text-muted-foreground bg-muted/50 rounded-lg">
                    <Package className="h-12 w-12 mx-auto mb-3 opacity-50" />
                    <p className="font-medium mb-2">Nu există produse disponibile pentru service</p>
                    <p className="text-sm">Toate produsele din comenzile tale finalizate au deja cereri de service active sau au fost anulate.</p>
                    <p className="text-xs mt-2 text-muted-foreground/70">Pentru a crea o nouă cerere de service, așteaptă finalizarea cererilor existente.</p>
                  </div>
                ) : (
                  <RadioGroup
                    onValueChange={(value) => setValue("orderItemId", value)}
                    className="space-y-3"
                  >
                    {availableProducts.map((product) => (
                      <div key={product.orderItemId} className="flex items-start space-x-3">
                        <RadioGroupItem
                          value={product.orderItemId}
                          id={product.orderItemId}
                          className="mt-1"
                        />
                        <Label
                          htmlFor={product.orderItemId}
                          className="flex-1 cursor-pointer"
                        >
                          <div className="flex items-start gap-3 p-3 border rounded-lg hover:bg-muted/50 transition-colors">
                            {product.image && product.image.length > 0 ? (
                              <Image
                                src={product.image[0]}
                                alt={product.description}
                                width={100}
                                height={100}
                                className="w-12 h-12 object-cover rounded-md flex-shrink-0"
                              />
                            ) : (
                              <div className="w-12 h-12 bg-muted rounded-md flex items-center justify-center flex-shrink-0">
                                <Package className="h-6 w-6 text-muted-foreground" />
                              </div>
                            )}
                            <div className="flex-1 min-w-0">
                              <p className="font-medium text-sm">{product.description}</p>
                              <p className="text-xs text-muted-foreground">
                                Cod: {product.materialNumber}
                              </p>
                              <p className="text-xs text-muted-foreground">
                                Comandă: #{product.orderNumber} • Cantitate: {product.quantity}
                              </p>
                            </div>
                          </div>
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                )}
                {errors.orderItemId && (
                  <p className="text-sm text-destructive mt-1">{errors.orderItemId.message}</p>
                )}
              </div>

              {/* Product selection now includes visual preview, so no separate preview needed */}
            </CardContent>
          </Card>

          {/* Issue Details */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Detalii Problemă
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="issueType">Tipul problemei *</Label>
                <Select onValueChange={(value) => setValue("issueType", value as IssueType)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selectează tipul problemei" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(IssueType).map((type) => (
                      <SelectItem key={type} value={type}>
                        {getIssueTypeLabel(type)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select> 
                {errors.issueType && (
                  <p className="text-sm text-destructive mt-1">{errors.issueType.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="description">Descrierea problemei *</Label>
                <Textarea
                  id="description"
                  placeholder="Descrie în detaliu problema întâmpinată cu produsul..."
                  className="min-h-[100px]"
                  {...register("description")}
                />
                {errors.description && (
                  <p className="text-sm text-destructive mt-1">{errors.description.message}</p>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  Minimum 10 caractere, maximum 2000 caractere
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Shipping Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Adresa de Service
              </CardTitle>
            </CardHeader>
            <CardContent>
              <AddressSelector
                addresses={availableAddresses}
                showrooms={showrooms}
                value={shippingSelection}
                onValueChange={setShippingSelection}
                className="space-y-4"
              />
              {!shippingSelection && (
                <p className="text-sm text-destructive mt-2">
                  Vă rugăm să selectați o metodă de livrare
                </p>
              )}
            </CardContent>
          </Card>

          {/* Info Notice */}
          <div className="bg-blue-50 dark:bg-blue-950 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 shrink-0" />
              <div className="text-sm">
                <p className="font-medium text-blue-900 dark:text-blue-100 mb-1">
                  Informații importante
                </p>
                <ul className="text-blue-800 dark:text-blue-200 space-y-1">
                  <li>• Vei primi un email de confirmare după înregistrarea cererii</li>
                  <li>• Echipa noastră te va contacta în maxim 24 de ore</li>
                  <li>• Poți urmări statusul cererii în secțiunea &quot;Service&quot;</li>
                </ul>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleClose}>
              Anulează
            </Button>
            <Button type="submit" disabled={isPending}>
              {isPending ? "Se creează..." : "Creează Cererea"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
