"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, CreditCard, MapPin } from "lucide-react";
import { BillingAddress, ShippingAddress, AddressType, AddressFormState, DeleteAddressState } from "@/types/addresses";
import AddressListItem from "./AddressListItem";
import AddressDialog from "./AddressDialog";
import DeleteAddressDialog from "./DeleteAddressDialog";

interface AddressListProps {
  addresses: BillingAddress[] | ShippingAddress[];
  type: AddressType;
  title: string;
  description: string;
  emptyStateMessage: string;
}

export default function AddressList({ 
  addresses, 
  type, 
  title, 
  description, 
  emptyStateMessage 
}: AddressListProps) {
  const [formState, setFormState] = useState<AddressFormState>({
    isOpen: false,
    mode: 'create',
    address: undefined,
  });

  const [deleteState, setDeleteState] = useState<DeleteAddressState>({
    isOpen: false,
    address: undefined,
    type,
  });

  const handleAddNew = () => {
    setFormState({
      isOpen: true,
      mode: 'create',
      address: undefined,
    });
  };

  const handleEdit = (address: BillingAddress | ShippingAddress) => {
    setFormState({
      isOpen: true,
      mode: 'edit',
      address,
    });
  };

  const handleDelete = (address: BillingAddress | ShippingAddress) => {
    setDeleteState({
      isOpen: true,
      address,
      type,
    });
  };

  const handleCloseForm = () => {
    setFormState({
      isOpen: false,
      mode: 'create',
      address: undefined,
    });
  };

  const handleCloseDelete = () => {
    setDeleteState({
      isOpen: false,
      address: undefined,
      type,
    });
  };

  const Icon = type === 'billing' ? CreditCard : MapPin;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <Icon className="h-6 w-6 text-primary" />
            <h1 className="text-2xl font-bold tracking-tight">{title}</h1>
          </div>
          <p className="text-muted-foreground">{description}</p>
        </div>
        <Button onClick={handleAddNew} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Adaugă adresă nouă
        </Button>
      </div>

      {/* Address List */}
      {addresses.length === 0 ? (
        <div className="text-center py-12">
          <Icon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Nicio adresă salvată</h3>
          <p className="text-muted-foreground mb-6 max-w-md mx-auto">
            {emptyStateMessage}
          </p>
          <Button onClick={handleAddNew} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Adaugă prima adresă
          </Button>
        </div>
      ) : (
        <div className="border rounded-lg overflow-hidden shadow">
          {addresses.map((address) => (
            <AddressListItem
              key={address.id}
              address={address}
              type={type}
              onEdit={handleEdit}
              onDelete={handleDelete}
            />
          ))}
        </div>
      )}

      {/* Dialogs */}
      <AddressDialog
        isOpen={formState.isOpen}
        onClose={handleCloseForm}
        type={type}
        mode={formState.mode}
        address={formState.address}
      />

      <DeleteAddressDialog
        isOpen={deleteState.isOpen}
        onClose={handleCloseDelete}
        address={deleteState.address}
        type={type}
      />
    </div>
  );
}
