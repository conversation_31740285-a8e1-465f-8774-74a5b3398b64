"use server"

import { getCurrentDbUser } from "@/lib/auth";
import { prisma, withRetry } from "@/lib/db";
import { logger } from "@/lib/logger";
import { revalidatePath } from "next/cache";
import { headers } from "next/headers";
import {
  createReturnSchema,
  updateReturnStatusSchema,
  cuidSchema,
  type CreateReturnInput,
  type UpdateReturnStatusInput,
} from "@/lib/zod";
import { ReturnActionResult } from "@/types/returns";
import { Prisma } from "@/generated/prisma";
import { sendReturnConfirmationEmail } from "@/lib/emailService";


/**
 * Generate a unique action number for OrderPostPurchaseAction
 */
async function generateActionNumber(): Promise<string> {
  const year = new Date().getFullYear();
  const prefix = `PPA-${year}-`;

  // Get the latest action number for this year
  const latestAction = await prisma.orderPostPurchaseAction.findFirst({
    where: {
      actionNumber: {
        startsWith: prefix,
      },
    },
    orderBy: {
      actionNumber: 'desc',
    },
    select: {
      actionNumber: true,
    },
  });

  let nextNumber = 1;
  if (latestAction) {
    const currentNumber = parseInt(latestAction.actionNumber.split('-')[2]);
    nextNumber = currentNumber + 1;
  }

  return `${prefix}${nextNumber.toString().padStart(5, '0')}`;
}

// Return reason labels
const RETURN_REASON_LABELS = {
  wrongItem: "Produs greșit",
  defective: "Produs defect",
  damaged: "Produs deteriorat",
  notAsDescribed: "Nu corespunde descrierii",
  noLongerWanted: "Nu mai doresc produsul",
  other: "Altul",
};

const getReturnReasonLabel = (reason: string): string => {
  return RETURN_REASON_LABELS[reason as keyof typeof RETURN_REASON_LABELS] || reason;
};

/**
 * Create a new return request
 */
export async function createReturn(input: CreateReturnInput): Promise<ReturnActionResult> {
  try {
    // Get current user
    const user = await getCurrentDbUser();
    if (!user) {
      logger.error(`[createReturn] No user authenticated`);
      return { success: false, error: "Utilizatorul nu este autentificat" };
    }

    // Validate input
    const validationResult = createReturnSchema.safeParse(input);
    if (!validationResult.success) {
      logger.warn(`[createReturn] Invalid input from user ${user.id}:`, { error: validationResult.error.message });
      return {
        success: false,
        error: "Datele introduse sunt invalide"
      };
    }

    const validatedInput = validationResult.data;

    // Get audit data
    const headerPayload = await headers();
    const auditData = {
      ipAddress: headerPayload.get('x-forwarded-for') || null,
      userAgent: headerPayload.get('user-agent') || null,
    };

    // Verify order belongs to user and get order items
    const order = await withRetry(() =>
      prisma.order.findFirst({
        where: {
          id: validatedInput.orderId,
          userId: user.id,
        },
        include: {
          orderItems: {
            include: {
              product: {
                select: {
                  Description_Local: true,
                  Material_Number: true,
                },
              },
              returnItems: {
                select: {
                  quantity: true,
                },
              },
            },
          },
        },
      })
    );

    if (!order) {
      logger.error(`[createReturn] Order ${validatedInput.orderId} not found for user ${user.id}`);
      return { success: false, error: "Comanda nu a fost găsită" };
    }

    // Validate that all requested items exist and have sufficient quantity
    for (const requestedItem of validatedInput.items) {
      const orderItem = order.orderItems.find(item => item.id === requestedItem.orderItemId);
      
      if (!orderItem) {
        logger.error(`[createReturn] Order item ${requestedItem.orderItemId} not found in order ${validatedInput.orderId}`);
        return { success: false, error: "Unul dintre produse nu a fost găsit în comandă" };
      }

      // Calculate already returned quantity
      const returnedQuantity = orderItem.returnItems.reduce((sum, returnItem) => sum + returnItem.quantity, 0);
      const availableQuantity = orderItem.quantity - returnedQuantity;

      if (requestedItem.quantity > availableQuantity) {
        logger.error(`[createReturn] Insufficient quantity for item ${requestedItem.orderItemId}. Requested: ${requestedItem.quantity}, Available: ${availableQuantity}`);
        return { success: false, error: `Cantitatea solicitată pentru unul dintre produse depășește cantitatea disponibilă pentru returnare` };
      }
    }

    // Create return in transaction with OrderPostPurchaseAction
    const result = await prisma.$transaction(async (tx) => {
      // Generate return number and action number
      const returnNumber = `RET-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`;
      const actionNumber = await generateActionNumber();

      // Create return
      const createdReturn = await tx.return.create({
        data: {
          returnNumber,
          orderId: validatedInput.orderId,
          method: validatedInput.method,
          addressId: validatedInput.addressId,
          showroomId: validatedInput.showroomId,
          status: 'requested',
          createdBy: user.id,
          returnItems: {
            create: validatedInput.items.map(item => ({
              orderItemId: item.orderItemId,
              quantity: item.quantity,
              reason: item.reason,
              description: item.description,
              condition: 'asDescribed', // Default condition
            })),
          },
        },
        select: {
          id: true,
          returnNumber: true,
        },
      });

      // Create OrderPostPurchaseAction for each returned item
      for (const item of validatedInput.items) {
        // Check if there's already an active action for this order item
        const existingAction = await tx.orderPostPurchaseAction.findFirst({
          where: {
            orderItemId: item.orderItemId,
            status: 'ACTIVE',
          },
        });

        if (existingAction) {
          logger.error(`[createReturn] Active action already exists for order item ${item.orderItemId}`);
          throw new Error(`Există deja o acțiune activă pentru unul dintre produse. Vă rugăm să verificați returnările existente.`);
        }

        await tx.orderPostPurchaseAction.create({
          data: {
            actionNumber: `${actionNumber}-${item.orderItemId.slice(-4)}`, // Make unique per item
            orderItemId: item.orderItemId,
            userId: user.id,
            type: 'RETURN',
            status: 'ACTIVE',
            returnId: createdReturn.id,
          },
        });
      }

      // Update order to mark it has returns
      await withRetry(() =>
        tx.order.update({
          where: { id: validatedInput.orderId },
          data: { hasReturns: true },
        })
      );

      // Create audit log
      await withRetry(() =>
        tx.userAuditLog.create({
          data: {
            userId: user.id,
            action: 'return.create',
            entityType: 'return',
            entityId: createdReturn.id,
            details: JSON.stringify({
              returnNumber: createdReturn.returnNumber,
              orderId: validatedInput.orderId,
              itemCount: validatedInput.items.length,
              items: validatedInput.items.map(item => ({ orderItemId: item.orderItemId, reason: item.reason, quantity: item.quantity })),
              ...auditData,
            }),
            ipAddress: auditData.ipAddress,
            userAgent: auditData.userAgent,
          },
        })
      );

      return createdReturn;
    });

    logger.info(`[createReturn] Return ${result.returnNumber} created successfully for user ${user.id}`);

    // Send return confirmation email
    const emailData = {
      returnNumber: result.returnNumber,
      customerName: `${user.firstName} ${user.lastName}`,
      customerEmail: user.email,
      orderNumber: order.orderNumber,
      items: validatedInput.items.map(item => {
        const orderItem = order.orderItems.find(oi => oi.id === item.orderItemId);
        return {
          name: orderItem?.product?.Description_Local || 'Produs necunoscut',
          code: orderItem?.product?.Material_Number || 'N/A',
          quantity: item.quantity,
          reason: getReturnReasonLabel(item.reason),
        };
      }),
    };

    const emailResult = await sendReturnConfirmationEmail(emailData);
    if (emailResult.success) {
      logger.info(`[createReturn] Return confirmation email sent for ${result.returnNumber}`);
    } else {
      logger.warn(`[createReturn] Failed to send return confirmation email for ${result.returnNumber}`);
    }

    // Revalidate relevant paths
    revalidatePath('/account/returns');
    revalidatePath(`/account/orders`);

    return {
      success: true,
      returnId: result.id,
      returnNumber: result.returnNumber,
    };

  } catch (error) {
    logger.error(`[createReturn] Error creating return:`, error);
    
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === 'P2002') {
        return { success: false, error: "Numărul de returnare există deja. Vă rugăm să încercați din nou." };
      }
    }

    return { 
      success: false, 
      error: "A apărut o eroare la crearea cererii de returnare. Vă rugăm să încercați din nou." 
    };
  }
}

/**
 * Cancel a return request (only if status is 'requested')
 */
export async function cancelReturn(returnId: string): Promise<ReturnActionResult> {
  try {
    // Get current user
    const user = await getCurrentDbUser();
    if (!user) {
      logger.error(`[cancelReturn] No user authenticated`);
      return { success: false, error: "Utilizatorul nu este autentificat" };
    }

    // Validate return ID
    const returnIdValidation = cuidSchema.safeParse(returnId);
    if (!returnIdValidation.success) {
      logger.error(`[cancelReturn] Invalid return ID: ${returnId}`);
      return { success: false, error: "ID-ul returnării este invalid" };
    }

    // Get audit data
    const headerPayload = await headers();
    const auditData = {
      ipAddress: headerPayload.get('x-forwarded-for') || null,
      userAgent: headerPayload.get('user-agent') || null,
    };

    // Find return and verify ownership
    const returnRecord = await withRetry(() =>
      prisma.return.findFirst({
        where: {
          id: returnId,
          order: {
            userId: user.id,
          },
        },
        select: {
          id: true,
          returnNumber: true,
          status: true,
          orderId: true,
        },
      })
    );

    if (!returnRecord) {
      logger.error(`[cancelReturn] Return ${returnId} not found for user ${user.id}`);
      return { success: false, error: "Returnarea nu a fost găsită" };
    }

    // Check if return can be cancelled
    if (returnRecord.status !== 'requested') {
      logger.error(`[cancelReturn] Cannot cancel return ${returnId} with status ${returnRecord.status}`);
      return { success: false, error: "Returnarea nu poate fi anulată în stadiul actual" };
    }

    // Update return status to cancelled
    await prisma.$transaction(async (tx) => {
      await withRetry(() =>
        tx.return.update({
          where: { id: returnId },
          data: {
            status: 'cancelled',
            updatedBy: user.id,
          },
        })
      );

      // Create audit log
      await withRetry(() =>
        tx.userAuditLog.create({
          data: {
            userId: user.id,
            action: 'return.cancel',
            entityType: 'return',
            entityId: returnId,
            details: JSON.stringify({
              returnNumber: returnRecord.returnNumber,
              previousStatus: returnRecord.status,
              ...auditData,
            }),
            ipAddress: auditData.ipAddress,
            userAgent: auditData.userAgent,
          },
        })
      );
    });

    logger.info(`[cancelReturn] Return ${returnRecord.returnNumber} cancelled successfully by user ${user.id}`);

    // Revalidate relevant paths
    revalidatePath('/account/returns');

    return {
      success: true,
      returnId: returnRecord.id,
      returnNumber: returnRecord.returnNumber,
    };

  } catch (error) {
    logger.error(`[cancelReturn] Error cancelling return:`, error);
    return { 
      success: false, 
      error: "A apărut o eroare la anularea returnării. Vă rugăm să încercați din nou." 
    };
  }
}

/**
 * Update return status (admin function - for future use)
 */
export async function updateReturnStatus(input: UpdateReturnStatusInput): Promise<ReturnActionResult> {
  try {
    // Get current user
    const user = await getCurrentDbUser();
    if (!user) {
      logger.error(`[updateReturnStatus] No user authenticated`);
      return { success: false, error: "Utilizatorul nu este autentificat" };
    }

    // For now, only allow admin users to update return status
    // You can modify this based on your role system
    if (!user.role?.includes('admin')) {
      logger.error(`[updateReturnStatus] User ${user.id} not authorized to update return status`);
      return { success: false, error: "Nu aveți permisiunea să actualizați statusul returnării" };
    }

    // Validate input
    const validationResult = updateReturnStatusSchema.safeParse(input);
    if (!validationResult.success) {
      logger.warn(`[updateReturnStatus] Invalid input:`, { error: validationResult.error.message });
      return { success: false, error: "Datele introduse sunt invalide" };
    }

    const validatedInput = validationResult.data;

    // Get audit data
    const headerPayload = await headers();
    const auditData = {
      ipAddress: headerPayload.get('x-forwarded-for') || null,
      userAgent: headerPayload.get('user-agent') || null,
    };

    // Update return status
    const updatedReturn = await withRetry(() =>
      prisma.return.update({
        where: { id: validatedInput.returnId },
        data: {
          status: validatedInput.status,
          updatedBy: user.id,
          // Set specific timestamps based on status
          ...(validatedInput.status === 'approved' && { approvedAt: new Date(), approvedBy: user.id }),
          ...(validatedInput.status === 'received' && { receivedAt: new Date() }),
          ...(validatedInput.status === 'inspected' && { inspectedAt: new Date() }),
          ...(validatedInput.status === 'refundIssued' && { refundedAt: new Date() }),
        },
        select: {
          id: true,
          returnNumber: true,
          status: true,
        },
      })
    );

    // Create audit log
    await withRetry(() =>
      prisma.userAuditLog.create({
        data: {
          userId: user.id,
          action: 'return.status_update',
          entityType: 'return',
          entityId: validatedInput.returnId,
          details: JSON.stringify({
            returnNumber: updatedReturn.returnNumber,
            newStatus: validatedInput.status,
            notes: validatedInput.notes,
            ...auditData,
          }),
          ipAddress: auditData.ipAddress,
          userAgent: auditData.userAgent,
        },
      })
    );

    logger.info(`[updateReturnStatus] Return ${updatedReturn.returnNumber} status updated to ${validatedInput.status} by user ${user.id}`);

    // Revalidate relevant paths
    revalidatePath('/account/returns');

    return {
      success: true,
      returnId: updatedReturn.id,
      returnNumber: updatedReturn.returnNumber,
    };

  } catch (error) {
    logger.error(`[updateReturnStatus] Error updating return status:`, error);
    
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      if (error.code === 'P2025') {
        return { success: false, error: "Returnarea nu a fost găsită" };
      }
    }

    return {
      success: false,
      error: "A apărut o eroare la actualizarea statusului returnării. Vă rugăm să încercați din nou."
    };
  }
}

// Removed getReturnableItems server action - now using getReturnableOrderItems from getData/returns.ts
