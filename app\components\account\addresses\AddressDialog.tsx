"use client";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { BillingAddress, ShippingAddress, AddressType } from "@/types/addresses";
import AddressForm from "./AddressForm";

interface AddressDialogProps {
  isOpen: boolean;
  onClose: () => void;
  type: AddressType;
  mode: 'create' | 'edit';
  address?: BillingAddress | ShippingAddress;
}

export default function AddressDialog({ 
  isOpen, 
  onClose, 
  type, 
  mode, 
  address 
}: AddressDialogProps) {
  const isBilling = type === 'billing';
  
  const getTitle = () => {
    if (mode === 'create') {
      return isBilling ? 'Adaugă adresă de facturare' : 'Adaugă adresă de livrare';
    } else {
      return isBilling ? 'Editează adresa de facturare' : 'Editează adresa de livrare';
    }
  };

  const getDescription = () => {
    if (mode === 'create') {
      return isBilling 
        ? 'Completați informațiile pentru noua adresă de facturare.'
        : 'Completați informațiile pentru noua adresă de livrare.';
    } else {
      return isBilling
        ? 'Modificați informațiile adresei de facturare.'
        : 'Modificați informațiile adresei de livrare.';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{getTitle()}</DialogTitle>
          <DialogDescription>
            {getDescription()}
          </DialogDescription>
        </DialogHeader>
        
        <div className="mt-4">
          <AddressForm
            type={type}
            mode={mode}
            address={address}
            onSuccess={onClose}
            onCancel={onClose}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}
