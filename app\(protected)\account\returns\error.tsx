"use client"

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertTriangle, RefreshCw } from "lucide-react";
import { useEffect } from "react";

interface ReturnsErrorProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function ReturnsError({ error, reset }: ReturnsErrorProps) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Returns page error:", error);
  }, [error]);

  return (
    <div className="flex items-center justify-center min-h-[400px]">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
            <AlertTriangle className="h-6 w-6 text-destructive" />
          </div>
          <CardTitle className="text-xl"><PERSON><PERSON>re la încărcarea returnărilor</CardTitle>
        </CardHeader>
        <CardContent className="text-center space-y-4">
          <p className="text-muted-foreground">
            A apărut o eroare neașteptată la încărcarea returnărilor tale. 
            Vă rugăm să încercați din nou.
          </p>
          
          {process.env.NODE_ENV === "development" && (
            <details className="text-left">
              <summary className="cursor-pointer text-sm font-medium mb-2">
                Detalii eroare (doar în dezvoltare)
              </summary>
              <pre className="text-xs bg-muted p-2 rounded overflow-auto">
                {error.message}
              </pre>
            </details>
          )}

          <div className="flex flex-col sm:flex-row gap-2 justify-center">
            <Button onClick={reset} className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              Încearcă din nou
            </Button>
            <Button variant="outline" onClick={() => window.location.href = "/account"}>
              Înapoi la cont
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
