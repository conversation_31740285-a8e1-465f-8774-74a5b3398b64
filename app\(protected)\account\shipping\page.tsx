"server-only"

import { getCurrentDbUser } from "@/lib/auth";
import { getUserShippingAddresses } from "@/app/getData/addresses";
import { redirect } from "next/navigation";
import { logger } from "@/lib/logger";
import AddressList from "@/app/components/account/addresses/AddressList";

export default async function ShippingPage() {
  try {
    // Get current authenticated user
    const user = await getCurrentDbUser();

    if (!user) {
      logger.warn('[ShippingPage] No authenticated user found');
      redirect("/sign-in");
    }

    // Fetch shipping addresses
    const addresses = await getUserShippingAddresses(user.id);

    return (
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        <AddressList
          addresses={addresses}
          type="shipping"
          title="Adrese de livrare"
          description="Gestionați adresele de livrare pentru comenzile dumneavoastră"
          emptyStateMessage="Nu aveți încă adrese de livrare salvate. Adăugați prima adresă pentru a facilita procesul de comandă."
        />
      </div>
    );

  } catch (error) {
    logger.error('[ShippingPage] Error loading shipping page:', error);
    redirect("/account/settings");
  }
}