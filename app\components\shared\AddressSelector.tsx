//claude
"use client";

import { useState, useEffect } from "react";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MapPin, Building2, Truck } from "lucide-react";
import { AddressSelectorProps, ShippingMethodType } from "@/types/addresses";
import Link from "next/link";


export default function AddressSelector({
  addresses,
  showrooms = [],
  value,
  onValueChange,
  className = "",
}: AddressSelectorProps) {
  const [selectedMethod, setSelectedMethod] = useState<ShippingMethodType>(
    value?.method || "intern" // Default to intern if no addresses
  );
  const [selectedAddressId, setSelectedAddressId] = useState<string>(value?.addressId || "");
  const [selectedShowroomId, setSelectedShowroomId] = useState<string>(value?.showroomId || "");

  // Initialize with default selection on mount
  useEffect(() => {
    if (!value) {
      // If no value is provided, set default based on available options
      if (addresses.length > 0) {
        // If user has addresses, default to courier with first/default address
        const defaultAddress = addresses.find(addr => addr.isDefault) || addresses[0];
        setSelectedMethod("curier");
        setSelectedAddressId(defaultAddress.id);
        onValueChange({ method: 'curier', addressId: defaultAddress.id });
      } else {
        // If no addresses, default to intern
        setSelectedMethod("intern");
        onValueChange({ method: 'intern' });
      }
    } else {
      // If value is provided, use it
      setSelectedMethod(value.method);
      setSelectedAddressId(value.addressId || "");
      setSelectedShowroomId(value.showroomId || "");
    }
  }, [value, addresses, onValueChange]); // Only run on mount

  // Update when value prop changes (but not on mount)
  useEffect(() => {
    if (value) {
      setSelectedMethod(value.method);
      setSelectedAddressId(value.addressId || "");
      setSelectedShowroomId(value.showroomId || "");
    }
  }, [value]);

  const handleMethodChange = (value: string) => {
    const method = value as ShippingMethodType;
    setSelectedMethod(method);

    if (method === 'intern') {
      // Internal shipment doesn't need additional selection
      onValueChange({ method: 'intern' });
    } else if (method === 'curier') {
      if (addresses.length === 0) {
        // No addresses available, just set the method
        onValueChange({ method: 'curier' });
      } else if (selectedAddressId) {
        // If we already have a saved address selected, use it
        onValueChange({ method: 'curier', addressId: selectedAddressId });
      } else {
        // Auto-select first/default address
        const defaultAddress = addresses.find(addr => addr.isDefault) || addresses[0];
        if (defaultAddress) {
          setSelectedAddressId(defaultAddress.id);
          onValueChange({ method: 'curier', addressId: defaultAddress.id });
        }
      }
    } else if (method === 'showroom') {
      if (selectedShowroomId) {
        // If we already have a showroom selected, use it
        onValueChange({ method: 'showroom', showroomId: selectedShowroomId });
      } else {
        // Clear selection until user selects a showroom
        onValueChange({ method: 'showroom' });
      }
    }
  };

  const handleAddressChange = (addressId: string) => {
    setSelectedAddressId(addressId);
    onValueChange({ method: 'curier', addressId });
  };

  const handleShowroomChange = (showroomId: string) => {
    setSelectedShowroomId(showroomId);
    onValueChange({ method: 'showroom', showroomId });
  };

  // Determine if courier option should be disabled
  const isCourierDisabled = addresses.length === 0;

  return (
    <div className={className}>
      <Label className="text-base font-medium mb-4 block">
        Selectează metoda de livrare
      </Label>

      <div className="space-y-6">
        {/* Shipping Method Selection */}
        <RadioGroup value={selectedMethod} onValueChange={handleMethodChange} className="space-y-3">
          {/* Courier Option */}
          <div className="flex items-start space-x-2">
            <RadioGroupItem value="curier" id="curier" disabled={isCourierDisabled} />
            <div className="flex-1">
              <Label 
                htmlFor="curier" 
                className={`flex items-center gap-2 cursor-pointer ${isCourierDisabled ? 'opacity-50' : ''}`}
              >
                <MapPin className="h-4 w-4" />
                Livrare prin curier
              </Label>
              {isCourierDisabled && (
                <div className="p-3 bg-muted/50 rounded-md">
                  <p className="text-sm text-muted-foreground mb-2">
                  Nu aveți adrese de livrare salvate.
                  </p>
                  <Link href="/account/shipping" className="text-sm text-primary hover:underline" >
                  Adăugați o adresă de livrare 
                  </Link>
                </div>
              )}
            </div>
          </div>

          {/* Showroom Pickup Option */}
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="showroom" id="showroom" />
            <Label htmlFor="showroom" className="flex items-center gap-2 cursor-pointer">
              <Building2 className="h-4 w-4" />
              Ridicare din showroom
            </Label>
          </div>

          {/* Internal Shipment Option */}
          <div className="flex items-center space-x-2">
            <RadioGroupItem value="intern" id="intern" />
            <Label htmlFor="intern" className="flex items-center gap-2 cursor-pointer">
              <Truck className="h-4 w-4" />
              Transport intern
            </Label>
          </div>
        </RadioGroup>

        {/* Courier Address Selection or No Address Message */}
        {selectedMethod === 'curier' && (
          <div className="space-y-2">
            {addresses.length > 0 ? (
              <>
                <Label className="text-sm font-medium">Selectează adresa de livrare</Label>
                <Select value={selectedAddressId} onValueChange={handleAddressChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="-- Selectează o adresă salvată --" />
                  </SelectTrigger>
                  <SelectContent>
                    {addresses.map((address) => (
                      <SelectItem key={address.id} value={address.id}>
                        <div className="flex flex-col">
                          <span className="font-medium">{address.fullName}</span>
                          <span className="text-sm text-muted-foreground">
                            {address.address}, {address.city}, {address.county}
                          </span>
                          {address.isDefault && (
                            <span className="text-xs text-primary">Adresă implicită</span>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </>
            ) : (
              <div className="p-3 bg-muted/50 rounded-md text-sm">
                  <Link
                    href="/account/shipping"
                    className="text-sm text-primary hover:underline"
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent event bubbling
                    }}
                  >
                    Adăugați o adresă de livrare →
                  </Link>
              </div>
            )}
          </div>
        )}

        {/* Showroom Selection */}
        {selectedMethod === 'showroom' && (
          <div className="space-y-2">
            <Label className="text-sm font-medium">Selectează showroom-ul</Label>
            <Select value={selectedShowroomId} onValueChange={handleShowroomChange}>
              <SelectTrigger>
                <SelectValue placeholder="-- Selectează un showroom --" />
              </SelectTrigger>
              <SelectContent>
                {showrooms.map((showroom) => (
                  <SelectItem key={showroom.id} value={showroom.id}>
                    <div className="flex flex-col">
                      <span className="font-medium">{showroom.name}</span>
                      <span className="text-sm text-muted-foreground">
                        {showroom.address1}, {showroom.city}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Internal Shipment Info */}
        {selectedMethod === 'intern' && (
          <div className="p-3 bg-muted/50 rounded-md">
            <p className="text-sm text-muted-foreground">
              Livrare prin serviciul intern de transport - nu sunt necesare date suplimentare.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
