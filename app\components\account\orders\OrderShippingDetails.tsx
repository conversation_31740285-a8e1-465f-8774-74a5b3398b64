import { getStatusLabel } from "@/lib/order-utils";
import { Order } from "@/types/orders";

// A new component to handle the display logic
export const OrderShippingDetails = ({ order }: { order: Order }) => {
  // A helper function to render the common method and tracking info
  const renderMethodAndTracking = () => (
    <div className="mt-3 pt-3 border-t border-gray-200">
      <p className="font-medium">Metoda livrare</p>
      <p>{getStatusLabel("shippingMethod", order.shippingMethod)}</p>
      {/* {order.tracking && (
        <p className="mt-1">
          Numar AWB:{" "}
          <a
            href={`https://fancourier.ro/awb-tracking/?awb=${order.tracking}`} // Example for FanCourier
            target="_blank"
            rel="noopener noreferrer"
            className="font-medium text-blue-600 hover:underline"
          >
            {order.tracking}
          </a>
        </p>
      )} */}
    </div>
  );

  switch (order.shippingMethod) {
    // Case 1: Standard Courier Delivery
    case 'curier':
      if (!order.shipping) return null; // Safety check
      return (
        <div className="space-y-2">
          <h4 className="font-medium">Date livrare curier</h4>
          <div className="rounded-lg p-4 text-sm ">
            <p className="font-medium">{order.shipping.fullName}</p>
            <p>{order.shipping.address}</p>
            <p>
              {order.shipping.city}, {order.shipping.county}
            </p>
            <p className="text-gray-500">Telefon: {order.shipping.phoneNumber}</p>
            {order.shipping.notes && (
              <p className="text-gray-500">Nota: {order.shipping.notes}</p>
            )}
            {renderMethodAndTracking()}
          </div>
        </div>
      );

    // Case 2: Showroom Pickup
    case 'showroom':
      if (!order.showroom) return null; // Safety check
      return (
        <div className="space-y-2">
          <h4 className="font-medium">Detalii ridicare personala</h4>
          <div className="rounded-lg p-4 text-sm">
            <p className="font-bold">{order.showroom.name}</p>
            <p>{order.showroom.address1}, {order.showroom.city}</p>
            {order.showroom.phone && <p className="text-gray-500">Telefon: {order.showroom.phone}</p>}
            {order.showroom.program && <p className="text-gray-500">Program: {order.showroom.program}</p>}

            <div className="mt-3 pt-3 border-t border-gray-200">
              <p className="font-medium">Metoda livrare</p>
              <p>{getStatusLabel("shippingMethod", order.shippingMethod)}</p>
              <p className="text-sm text-gray-500 mt-1">
                Veti fi notificat cand comanda este gata de ridicare.
              </p>
            </div>
          </div>
        </div>
      );

    // Case 3: Transport intern
    case 'intern':
      return (
        <div className="space-y-2">
          <h4 className="font-medium">Detalii transport intern</h4>
          <div className="rounded-lg p-4 text-sm">
            <p className="text-sm text-gray-500">
              Livrare prin serviciul intern de transport - nu sunt necesare date suplimentare.
            </p>
            <div className="mt-3 pt-3 border-t border-gray-200">
              <p className="font-medium">Metoda livrare</p>
              <p>{getStatusLabel("shippingMethod", order.shippingMethod)}</p>
            </div>
          </div>
        </div>
      );

    // Default fallback case
    default:
      return (
        <div className="space-y-2">
          <h4 className="font-medium">Metoda livrare</h4>
          <p>{getStatusLabel("shippingMethod", order.shippingMethod)}</p>
        </div>
      );
  }
};