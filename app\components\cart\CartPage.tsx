"use client"

import { useState } from "react";
import { formatPriceRON } from "@/lib/utils";
import { Cart, ShippingCalculator } from "@/types/cart";
import { ShippingAddress, ShippingSelection, ShowroomData } from "@/types/addresses";
import CartPageItem from "./CartPageItem";
import CartObservatiiRoute from "./CartObservatiiRoute";
import { CartButtonOrder } from "./CartButtonOrder";
import AddressSelector from "@/app/components/shared/AddressSelector";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";


interface CartPageProps {
  cart: Cart;
  shippingAddresses: ShippingAddress[];
  showrooms: ShowroomData[];
  has4th: boolean;
}

const CartPage = ({ cart, shippingAddresses, showrooms, has4th }: CartPageProps) => {
  const [shippingSelection, setShippingSelection] = useState<ShippingSelection | undefined>();
  const [acceptTerms, setAcceptTerms] = useState(false);
 
  const activeItems = cart.items.filter(item => item.addToOrder);
  
  const subtotal = activeItems.reduce(
    (acc, item) => acc + (item.FinalPrice ?? 0) * item.quantity,
    0
  );
  
  const totalWeight = activeItems.reduce(
    (acc, item) => acc + (item.Net_Weight ?? 0) * item.quantity,
    0
  );

  const shipping = ShippingCalculator.calculateShipping({
    subtotal,
    totalWeight: totalWeight * 0.1,
    shippingMethod: shippingSelection?.method || 'curier',
  });
  
  const total = subtotal + shipping;
  
  return (
    <div className="min-h-screen py-12">
      <div className="max-w-[1640px] mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="text-2xl font-semibold mb-8">
          Cosul tau ({cart.items.length} produse)
        </h1>

        <div className="flex flex-col lg:flex-row items-start justify-between gap-8">
          {/* Cart Items */}
          <div className="w-full lg:w-2/3 border rounded-lg shadow p-6">
            {cart.items.map((item) => (
              <div
                key={item.id}
                className="flex items-center gap-6 p-6 border-b last:border-0"
              >
                {/* Item Details*/}
                <CartPageItem item={item} />

                {/* Item Price */}
                <div className="text-right">
                  <p className="text-lg font-semibold">
                    {formatPriceRON((item.FinalPrice ? item.FinalPrice : 0) * item.quantity)}
                  </p>
                  <p className="text-sm text-gray-500 mt-1">
                    {formatPriceRON(item.FinalPrice)} buc
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Order Summary */}
          <div className="w-full lg:w-1/3 border rounded-lg shadow p-6 h-fit">
            <h2 className="text-xl font-semibold mb-6">
              Sumar comanda
            </h2>

            <div className="space-y-4">
              <div className="flex justify-between">
                <span>Subtotal</span>
                <span>{ formatPriceRON(subtotal) }</span>
              </div>
              <div className="flex justify-between">
                <span>Taxa de livrare</span>
                <span>
                  {formatPriceRON(shipping)}
                </span>
              </div>
              {shipping > 0 && ( <p className="text-xs text-muted-foreground">*Transport gratuit pentru comenzi peste 200 RON</p> )}
              {has4th ? (
                <p className="text-xs text-muted-foreground">
                  *Preturile nu includ TVA
                </p>
              ) : (
                <p className="text-xs text-muted-foreground">
                  *Preturile includ TVA
                </p>
              )}
              <div className="border-t border-gray-200 pt-4">
                <div className="flex justify-between font-semibold text-lg">
                  <span>Total</span>
                  <span>{formatPriceRON(total)}</span>
                </div>
              </div>
              {/*textarea for notes*/}
              <CartObservatiiRoute cart={cart} />
            </div>

            <Separator className="my-6" />

            {/* Address Selection */}
            <div className="space-y-4">
              <AddressSelector
                addresses={shippingAddresses}
                showrooms={showrooms}
                value={shippingSelection}
                onValueChange={setShippingSelection}
                className="space-y-4"
              />
            </div>

            <Separator className="my-6" />

            {/* Terms and Conditions */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="accept-terms"
                checked={acceptTerms}
                onCheckedChange={(checked) => setAcceptTerms(checked === true)}
              />
              <Label
                htmlFor="accept-terms"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Accept termenii și condițiile pentru comandă
              </Label>
            </div>

            <CartButtonOrder
              shippingSelection={shippingSelection}
              acceptTerms={acceptTerms}
            />
          </div>
          
        </div>
      </div>
    </div>
  );
};

export default CartPage;
