'use server';

import { revalidatePath } from 'next/cache';
import { getCurrentDbUser } from '@/lib/auth';
import { logger } from '@/lib/logger';
import { prisma, withRetry } from '@/lib/db';
import { 
  billingAddressSchema, 
  shippingAddressSchema, 
  cuidSchema,
  type BillingAddressInput,
  type ShippingAddressInput 
} from '@/lib/zod';
import { AddressActionResult } from '@/types/addresses';

// BILLING ADDRESS ACTIONS

/**
 * Creates a new billing address for the current user
 */
export async function createBillingAddress(input: BillingAddressInput): Promise<AddressActionResult> {
  try {
    const user = await getCurrentDbUser();
    if (!user) {
      logger.error('[createBillingAddress] No user authenticated');
      return { success: false, error: 'Nu sunteți autentificat' };
    }

    // Validate input
    const validationResult = billingAddressSchema.safeParse(input);
    if (!validationResult.success) {
      logger.error(`[createBillingAddress] Validation failed for user ${user.id}:`, validationResult.error.flatten().fieldErrors);
      return { 
        success: false, 
        error: 'Datele introduse nu sunt valide' 
      };
    }

    const validatedData = validationResult.data;

    // Create the address
    const address = await withRetry(() =>
      prisma.billingAddress.create({
        data: {
          userId: user.id,
          fullName: validatedData.fullName,
          companyName: validatedData.companyName || null,
          address: validatedData.address,
          city: validatedData.city,
          county: validatedData.county,
          cui: validatedData.cui || null,
          bank: validatedData.bank || null,
          iban: validatedData.iban || null,
          isDefault: false, // New addresses are not default by default
        },
        select: {
          id: true,
          fullName: true,
          companyName: true,
          address: true,
          city: true,
          county: true,
          cui: true,
          bank: true,
          iban: true,
          isDefault: true,
          createdAt: true,
          updatedAt: true,
        }
      })
    );

    // Convert to clean interface
    const cleanAddress = {
      id: address.id,
      fullName: address.fullName,
      companyName: address.companyName || undefined,
      address: address.address,
      city: address.city,
      county: address.county,
      cui: address.cui || undefined,
      bank: address.bank || undefined,
      iban: address.iban || undefined,
      isDefault: address.isDefault,
      createdAt: address.createdAt.toISOString(),
      updatedAt: address.updatedAt.toISOString(),
    };

    revalidatePath('/account/billing');
    logger.info(`[createBillingAddress] Created billing address for user ${user.id}: ${address.id}`);
    
    return { 
      success: true, 
      data: cleanAddress 
    };

  } catch (error) {
    logger.error('[createBillingAddress] Error creating billing address:', error);
    return { 
      success: false, 
      error: 'A apărut o eroare la salvarea adresei de facturare' 
    };
  }
}

/**
 * Updates an existing billing address
 */
export async function updateBillingAddress(addressId: string, input: BillingAddressInput): Promise<AddressActionResult> {
  try {
    const user = await getCurrentDbUser();
    if (!user) {
      logger.error('[updateBillingAddress] No user authenticated');
      return { success: false, error: 'Nu sunteți autentificat' };
    }

    // Validate address ID
    const addressIdValidation = cuidSchema.safeParse(addressId);
    if (!addressIdValidation.success) {
      logger.error(`[updateBillingAddress] Invalid address ID: ${addressId}`);
      return { success: false, error: 'ID-ul adresei nu este valid' };
    }

    // Validate input
    const validationResult = billingAddressSchema.safeParse(input);
    if (!validationResult.success) {
      logger.error(`[updateBillingAddress] Validation failed for user ${user.id}:`, validationResult.error.flatten().fieldErrors);
      return { 
        success: false, 
        error: 'Datele introduse nu sunt valide' 
      };
    }

    const validatedData = validationResult.data;

    // Update the address (only if it belongs to the user)
    const address = await withRetry(() =>
      prisma.billingAddress.update({
        where: {
          id: addressId,
          userId: user.id, // Ensure user owns the address
        },
        data: {
          fullName: validatedData.fullName,
          companyName: validatedData.companyName || null,
          address: validatedData.address,
          city: validatedData.city,
          county: validatedData.county,
          cui: validatedData.cui || null,
          bank: validatedData.bank || null,
          iban: validatedData.iban || null,
        },
        select: {
          id: true,
          fullName: true,
          companyName: true,
          address: true,
          city: true,
          county: true,
          cui: true,
          bank: true,
          iban: true,
          isDefault: true,
          createdAt: true,
          updatedAt: true,
        }
      })
    );

    // Convert to clean interface
    const cleanAddress = {
      id: address.id,
      fullName: address.fullName,
      companyName: address.companyName || undefined,
      address: address.address,
      city: address.city,
      county: address.county,
      cui: address.cui || undefined,
      bank: address.bank || undefined,
      iban: address.iban || undefined,
      isDefault: address.isDefault,
      createdAt: address.createdAt.toISOString(),
      updatedAt: address.updatedAt.toISOString(),
    };

    revalidatePath('/account/billing');
    logger.info(`[updateBillingAddress] Updated billing address for user ${user.id}: ${addressId}`);
    
    return { 
      success: true, 
      data: cleanAddress 
    };

  } catch (error) {
    if (error instanceof Error && error.message.includes('Record to update not found')) {
      logger.warn(`[updateBillingAddress] Address not found or not owned by user: ${addressId}`);
      return { success: false, error: 'Adresa nu a fost găsită' };
    }
    
    logger.error('[updateBillingAddress] Error updating billing address:', error);
    return { 
      success: false, 
      error: 'A apărut o eroare la actualizarea adresei de facturare' 
    };
  }
}

/**
 * Deletes a billing address
 */
export async function deleteBillingAddress(addressId: string): Promise<AddressActionResult> {
  try {
    const user = await getCurrentDbUser();
    if (!user) {
      logger.error('[deleteBillingAddress] No user authenticated');
      return { success: false, error: 'Nu sunteți autentificat' };
    }

    // Validate address ID
    const addressIdValidation = cuidSchema.safeParse(addressId);
    if (!addressIdValidation.success) {
      logger.error(`[deleteBillingAddress] Invalid address ID: ${addressId}`);
      return { success: false, error: 'ID-ul adresei nu este valid' };
    }

    // Delete the address (only if it belongs to the user)
    await withRetry(() =>
      prisma.billingAddress.delete({
        where: {
          id: addressId,
          userId: user.id, // Ensure user owns the address
        }
      })
    );

    revalidatePath('/account/billing');
    logger.info(`[deleteBillingAddress] Deleted billing address for user ${user.id}: ${addressId}`);
    
    return { success: true };

  } catch (error) {
    if (error instanceof Error && error.message.includes('Record to delete does not exist')) {
      logger.warn(`[deleteBillingAddress] Address not found or not owned by user: ${addressId}`);
      return { success: false, error: 'Adresa nu a fost găsită' };
    }
    
    logger.error('[deleteBillingAddress] Error deleting billing address:', error);
    return {
      success: false,
      error: 'A apărut o eroare la ștergerea adresei de facturare'
    };
  }
}

/**
 * Sets a billing address as default (and unsets the previous default)
 */
export async function setDefaultBillingAddress(addressId: string): Promise<AddressActionResult> {
  try {
    const user = await getCurrentDbUser();
    if (!user) {
      logger.error('[setDefaultBillingAddress] No user authenticated');
      return { success: false, error: 'Nu sunteți autentificat' };
    }

    // Validate address ID
    const addressIdValidation = cuidSchema.safeParse(addressId);
    if (!addressIdValidation.success) {
      logger.error(`[setDefaultBillingAddress] Invalid address ID: ${addressId}`);
      return { success: false, error: 'ID-ul adresei nu este valid' };
    }

    // Use transaction to ensure atomicity
    await withRetry(() =>
      prisma.$transaction(async (tx) => {
        // First, unset all default billing addresses for this user
        await tx.billingAddress.updateMany({
          where: {
            userId: user.id,
            isDefault: true,
          },
          data: {
            isDefault: false,
          }
        });

        // Then set the specified address as default
        await tx.billingAddress.update({
          where: {
            id: addressId,
            userId: user.id, // Ensure user owns the address
          },
          data: {
            isDefault: true,
          }
        });
      })
    );

    revalidatePath('/account/billing');
    logger.info(`[setDefaultBillingAddress] Set default billing address for user ${user.id}: ${addressId}`);

    return { success: true };

  } catch (error) {
    if (error instanceof Error && error.message.includes('Record to update not found')) {
      logger.warn(`[setDefaultBillingAddress] Address not found or not owned by user: ${addressId}`);
      return { success: false, error: 'Adresa nu a fost găsită' };
    }

    logger.error('[setDefaultBillingAddress] Error setting default billing address:', error);
    return {
      success: false,
      error: 'A apărut o eroare la setarea adresei implicite de facturare'
    };
  }
}

// SHIPPING ADDRESS ACTIONS

/**
 * Creates a new shipping address for the current user
 */
export async function createShippingAddress(input: ShippingAddressInput): Promise<AddressActionResult> {
  try {
    const user = await getCurrentDbUser();
    if (!user) {
      logger.error('[createShippingAddress] No user authenticated');
      return { success: false, error: 'Nu sunteți autentificat' };
    }

    // Validate input
    const validationResult = shippingAddressSchema.safeParse(input);
    if (!validationResult.success) {
      logger.error(`[createShippingAddress] Validation failed for user ${user.id}:`, validationResult.error.flatten().fieldErrors);
      return {
        success: false,
        error: 'Datele introduse nu sunt valide'
      };
    }

    const validatedData = validationResult.data;

    // Create the address
    const address = await withRetry(() =>
      prisma.shippingAddress.create({
        data: {
          userId: user.id,
          fullName: validatedData.fullName,
          address: validatedData.address,
          city: validatedData.city,
          county: validatedData.county,
          phoneNumber: validatedData.phoneNumber,
          notes: validatedData.notes || null,
          isDefault: false, // New addresses are not default by default
        },
        select: {
          id: true,
          fullName: true,
          address: true,
          city: true,
          county: true,
          phoneNumber: true,
          notes: true,
          isDefault: true,
          createdAt: true,
          updatedAt: true,
        }
      })
    );

    // Convert to clean interface
    const cleanAddress = {
      id: address.id,
      fullName: address.fullName,
      address: address.address,
      city: address.city,
      county: address.county,
      phoneNumber: address.phoneNumber,
      notes: address.notes || undefined,
      isDefault: address.isDefault,
      createdAt: address.createdAt.toISOString(),
      updatedAt: address.updatedAt.toISOString(),
    };

    revalidatePath('/account/shipping');
    logger.info(`[createShippingAddress] Created shipping address for user ${user.id}: ${address.id}`);

    return {
      success: true,
      data: cleanAddress
    };

  } catch (error) {
    logger.error('[createShippingAddress] Error creating shipping address:', error);
    return {
      success: false,
      error: 'A apărut o eroare la salvarea adresei de livrare'
    };
  }
}

/**
 * Updates an existing shipping address
 */
export async function updateShippingAddress(addressId: string, input: ShippingAddressInput): Promise<AddressActionResult> {
  try {
    const user = await getCurrentDbUser();
    if (!user) {
      logger.error('[updateShippingAddress] No user authenticated');
      return { success: false, error: 'Nu sunteți autentificat' };
    }

    // Validate address ID
    const addressIdValidation = cuidSchema.safeParse(addressId);
    if (!addressIdValidation.success) {
      logger.error(`[updateShippingAddress] Invalid address ID: ${addressId}`);
      return { success: false, error: 'ID-ul adresei nu este valid' };
    }

    // Validate input
    const validationResult = shippingAddressSchema.safeParse(input);
    if (!validationResult.success) {
      logger.error(`[updateShippingAddress] Validation failed for user ${user.id}:`, validationResult.error.flatten().fieldErrors);
      return {
        success: false,
        error: 'Datele introduse nu sunt valide'
      };
    }

    const validatedData = validationResult.data;

    // Update the address (only if it belongs to the user)
    const address = await withRetry(() =>
      prisma.shippingAddress.update({
        where: {
          id: addressId,
          userId: user.id, // Ensure user owns the address
        },
        data: {
          fullName: validatedData.fullName,
          address: validatedData.address,
          city: validatedData.city,
          county: validatedData.county,
          phoneNumber: validatedData.phoneNumber,
          notes: validatedData.notes || null,
        },
        select: {
          id: true,
          fullName: true,
          address: true,
          city: true,
          county: true,
          phoneNumber: true,
          notes: true,
          isDefault: true,
          createdAt: true,
          updatedAt: true,
        }
      })
    );

    // Convert to clean interface
    const cleanAddress = {
      id: address.id,
      fullName: address.fullName,
      address: address.address,
      city: address.city,
      county: address.county,
      phoneNumber: address.phoneNumber,
      notes: address.notes || undefined,
      isDefault: address.isDefault,
      createdAt: address.createdAt.toISOString(),
      updatedAt: address.updatedAt.toISOString(),
    };

    revalidatePath('/account/shipping');
    logger.info(`[updateShippingAddress] Updated shipping address for user ${user.id}: ${addressId}`);

    return {
      success: true,
      data: cleanAddress
    };

  } catch (error) {
    if (error instanceof Error && error.message.includes('Record to update not found')) {
      logger.warn(`[updateShippingAddress] Address not found or not owned by user: ${addressId}`);
      return { success: false, error: 'Adresa nu a fost găsită' };
    }

    logger.error('[updateShippingAddress] Error updating shipping address:', error);
    return {
      success: false,
      error: 'A apărut o eroare la actualizarea adresei de livrare'
    };
  }
}

/**
 * Deletes a shipping address
 */
export async function deleteShippingAddress(addressId: string): Promise<AddressActionResult> {
  try {
    const user = await getCurrentDbUser();
    if (!user) {
      logger.error('[deleteShippingAddress] No user authenticated');
      return { success: false, error: 'Nu sunteți autentificat' };
    }

    // Validate address ID
    const addressIdValidation = cuidSchema.safeParse(addressId);
    if (!addressIdValidation.success) {
      logger.error(`[deleteShippingAddress] Invalid address ID: ${addressId}`);
      return { success: false, error: 'ID-ul adresei nu este valid' };
    }

    // Delete the address (only if it belongs to the user)
    await withRetry(() =>
      prisma.shippingAddress.delete({
        where: {
          id: addressId,
          userId: user.id, // Ensure user owns the address
        }
      })
    );

    revalidatePath('/account/shipping');
    logger.info(`[deleteShippingAddress] Deleted shipping address for user ${user.id}: ${addressId}`);

    return { success: true };

  } catch (error) {
    if (error instanceof Error && error.message.includes('Record to delete does not exist')) {
      logger.warn(`[deleteShippingAddress] Address not found or not owned by user: ${addressId}`);
      return { success: false, error: 'Adresa nu a fost găsită' };
    }

    logger.error('[deleteShippingAddress] Error deleting shipping address:', error);
    return {
      success: false,
      error: 'A apărut o eroare la ștergerea adresei de livrare'
    };
  }
}

/**
 * Sets a shipping address as default (and unsets the previous default)
 */
export async function setDefaultShippingAddress(addressId: string): Promise<AddressActionResult> {
  try {
    const user = await getCurrentDbUser();
    if (!user) {
      logger.error('[setDefaultShippingAddress] No user authenticated');
      return { success: false, error: 'Nu sunteți autentificat' };
    }

    // Validate address ID
    const addressIdValidation = cuidSchema.safeParse(addressId);
    if (!addressIdValidation.success) {
      logger.error(`[setDefaultShippingAddress] Invalid address ID: ${addressId}`);
      return { success: false, error: 'ID-ul adresei nu este valid' };
    }

    // Use transaction to ensure atomicity
    await withRetry(() =>
      prisma.$transaction(async (tx) => {
        // First, unset all default shipping addresses for this user
        await tx.shippingAddress.updateMany({
          where: {
            userId: user.id,
            isDefault: true,
          },
          data: {
            isDefault: false,
          }
        });

        // Then set the specified address as default
        await tx.shippingAddress.update({
          where: {
            id: addressId,
            userId: user.id, // Ensure user owns the address
          },
          data: {
            isDefault: true,
          }
        });
      })
    );

    revalidatePath('/account/shipping');
    logger.info(`[setDefaultShippingAddress] Set default shipping address for user ${user.id}: ${addressId}`);

    return { success: true };

  } catch (error) {
    if (error instanceof Error && error.message.includes('Record to update not found')) {
      logger.warn(`[setDefaultShippingAddress] Address not found or not owned by user: ${addressId}`);
      return { success: false, error: 'Adresa nu a fost găsită' };
    }

    logger.error('[setDefaultShippingAddress] Error setting default shipping address:', error);
    return {
      success: false,
      error: 'A apărut o eroare la setarea adresei implicite de livrare'
    };
  }
}
