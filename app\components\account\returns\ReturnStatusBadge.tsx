"use client"

import { Badge } from "@/components/ui/badge";
import { ReturnStatus } from "@/types/returns";
import {
  Clock,
  CheckCircle,
  XCircle,
  Package,
} from "lucide-react";
//import type { ReturnStatus } from "@/generated/prisma";

// Return status display configuration
const RETURN_STATUS_CONFIG = {
  requested: { 
    label: "Solicitată", 
    color: "default" as const, 
    icon: Clock,
    description: "Cererea de returnare a fost trimisă și este în așteptarea aprobării"
  },
  approved: { 
    label: "Aprobată", 
    color: "secondary" as const, 
    icon: CheckCircle,
    description: "Cererea de returnare a fost aprobată"
  },
  rejected: { 
    label: "Respinsă", 
    color: "destructive" as const, 
    icon: XCircle,
    description: "Cererea de returnare a fost respinsă"
  },
  awaitingReceipt: { 
    label: "În așteptare", 
    color: "default" as const, 
    icon: Package,
    description: "Așteptăm să primim produsele returnate"
  },
  received: { 
    label: "Primită", 
    color: "secondary" as const, 
    icon: Package,
    description: "Produsele au fost primite în depozit"
  },
  inspected: { 
    label: "Verificată", 
    color: "secondary" as const, 
    icon: CheckCircle,
    description: "Produsele au fost verificate și evaluate"
  },
  refundIssued: { 
    label: "Rambursată", 
    color: "secondary" as const, 
    icon: CheckCircle,
    description: "Rambursarea a fost procesată"
  },
  completed: { 
    label: "Finalizată", 
    color: "secondary" as const, 
    icon: CheckCircle,
    description: "Procesul de returnare a fost finalizat cu succes"
  },
  cancelled: { 
    label: "Anulată", 
    color: "outline" as const, 
    icon: XCircle,
    description: "Cererea de returnare a fost anulată"
  },
};

interface ReturnStatusBadgeProps {
  status: ReturnStatus;
  showIcon?: boolean;
  showDescription?: boolean;
  className?: string;
}

export default function ReturnStatusBadge({ 
  status, 
  showIcon = true, 
  showDescription = false,
  className 
}: ReturnStatusBadgeProps) {
  const config = RETURN_STATUS_CONFIG[status];
  const Icon = config.icon;

  if (showDescription) {
    return (
      <div className="space-y-1">
        <Badge variant={config.color} className={`flex items-center gap-1 ${className}`}>
          {showIcon && <Icon className="h-3 w-3" />}
          {config.label}
        </Badge>
        <p className="text-xs text-muted-foreground">{config.description}</p>
      </div>
    );
  }

  return (
    <Badge variant={config.color} className={`flex items-center gap-1 ${className}`}>
      {showIcon && <Icon className="h-3 w-3" />}
      {config.label}
    </Badge>
  );
}

// Export the configuration for use in other components
export { RETURN_STATUS_CONFIG };
