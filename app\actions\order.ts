"use server"

import { getCurrentDbUser } from "@/lib/auth";
import { prisma, withRetry } from "@/lib/db";
import { sendOrderConfirmationEmail } from "@/lib/emailService";
import { logger } from "@/lib/logger";
import { redis } from "@/lib/redis";
import { Cart, ShippingCalculator } from "@/types/cart";
import { OrderDataEmail } from "@/types/email";
import { ShippingMethodType, ShippingSelection } from "@/types/addresses";
import { getUserShippingAddresses } from "@/app/getData/addresses";
import { revalidatePath } from "next/cache";
import { redirect, RedirectType } from "next/navigation";
import { toSafeNumber } from "@/lib/utils";

export async function placeOrder(shippingSelection: ShippingSelection): Promise<{
  success: boolean 
}> {

    const user = await getCurrentDbUser();
    if (!user) {
      logger.error(`[placeOrder] No user authenticated`);
      return { success: false };
    }

    if (!redis) {
      logger.error("[placeOrder] Redis connection not available");
      return { success: false };
    }

        // Validate shipping selection and prepare order data
    let shippingAddressId: string | null = null;
    let shippingMethod: ShippingMethodType = 'curier';
    let showroomId: string | null = null;

    // Courier delivery with saved address
    if (shippingSelection.method === 'curier' && shippingSelection.addressId) {
      // Courier delivery with saved address
      const userAddresses = await getUserShippingAddresses(user.id);
      const selectedAddress = userAddresses.find(addr => addr.id === shippingSelection.addressId);

      if (!selectedAddress) {
        logger.error(`[placeOrder] Selected address not found: ${shippingSelection.addressId}`);
        return { success: false };
      }

      shippingAddressId = selectedAddress.id;
      shippingMethod = 'curier';
    } else if (shippingSelection.method === 'intern') {
      // Internal shipment - no address needed
      shippingMethod = 'intern';
    } else if (shippingSelection.method === 'showroom' && shippingSelection.showroomId) {
      // Showroom pickup - validate showroom exists
      logger.info(`[placeOrder] Validating showroom: ${shippingSelection.showroomId}`);

      try {
        const showroom = await prisma.showroom.findUnique({
          where: { id: shippingSelection.showroomId, isActive: true },
          select: { id: true, name: true, isActive: true }
        });

        if (!showroom) {
          logger.error(`[placeOrder] Showroom not found or inactive: ${shippingSelection.showroomId}`);
          return { success: false };
        }

        logger.info(`[placeOrder] Showroom validated successfully: ${showroom.id} - ${showroom.name} (active: ${showroom.isActive})`);
        shippingMethod = 'showroom';
        showroomId = shippingSelection.showroomId;
      } catch (error) {
        logger.error(`[placeOrder] Error validating showroom: ${error}`);
        return { success: false };
      }
    } else {
      logger.error(`[placeOrder] Invalid shipping selection: ${JSON.stringify(shippingSelection)}`);
      return { success: false };
    }

    // Retrieve the cart from Redis
    const cartTotal = await redis.get<Cart>(`cart-${user.id}`);

    if (!cartTotal) {
      logger.error(`[placeOrder] No cart found for user ${user.id}`);
      return { success: false };
    }

    if (cartTotal.items.length === 0) {
      logger.error(`[placeOrder] No items in cart for user ${user.id}`);
      return { success: false };
    }

    // Filter cart items that should be ordered
    const cartItemsToOrder = cartTotal.items.filter(item => item.addToOrder);
    const remainingCartItems = cartTotal.items.filter(item => !item.addToOrder);

    if(cartItemsToOrder.length === 0){
      logger.error(`[placeOrder] No items selected for order for user ${user.id}`);
      return { success: false };
    }

    // Get fresh product data from database
    const productIds = cartItemsToOrder.map(item => item.id);
    let order: { id: string; orderNumber: string } | null = null;
    let orderData: OrderDataEmail | null = null;
    try {
      const freshProducts = await prisma.product.findMany({
        where: {
          id: { in: productIds }
        },
        select: {
          id: true,
          Material_Number: true,
          Net_Weight: true,
          ImageUrl: true,
          Description_Local: true,
          FinalPrice: true,
          isActive: true, // Assuming you have this field to check availability
          // Add any other fields you need for validation
        }
      });

      // Validate all products exist and are available
      const unavailableProducts = productIds.filter(id => 
        !freshProducts.find(p => p.id === id && p.isActive)
      );

      if (unavailableProducts.length > 0) {
        logger.error(`[placeOrder] Unavailable products: ${unavailableProducts.join(', ')}`);
        return { 
          success: false, 
        };
      }

      // Merge cart quantities/notes with fresh product data
      const orderItems = cartItemsToOrder.map(cartItem => {
        const freshProduct = freshProducts.find(p => p.id === cartItem.id);
        if (!freshProduct) {
          logger.error(`[placeOrder] Product not found: ${cartItem.id}`);
          return {
            ...cartItem,
            FinalPrice: 0,
          };
        }
        
        return {
          ...freshProduct,
          quantity: cartItem.quantity,
          vinNotes: cartItem.vinNotes,
          addVinNotesToInvoice: cartItem.addVinNotesToInvoice,
        };
      });

      // Calculate totals with fresh prices
      const subtotal = orderItems.reduce((acc, item) => 
        acc + (toSafeNumber(item.FinalPrice) ?? 0) * item.quantity, 0
      );

      //if the final price is 0 or null then return success false
      if(subtotal === 0){
        logger.error(`[placeOrder] Total amount is 0 for user ${user.id}`);
        return { success: false };
      }

      const totalWeight = orderItems.reduce((acc, item) => 
        acc + (toSafeNumber(item.Net_Weight) ?? 0) * item.quantity, 0
      );

      // Calculate shipping
      const shippingCost = ShippingCalculator.calculateShipping({
        subtotal,
        totalWeight: totalWeight * 0.1,
        shippingMethod,
      });

      const totalAmount = subtotal + shippingCost;



      //try {
        logger.info(`[placeOrder] Creating order with showroomId: ${showroomId}, shippingMethod: ${shippingMethod}`);

        order = await prisma.$transaction(async (tx) => {
          const createdOrder = await withRetry(() =>
            tx.order.create({
              data: {
                userId: user.id,
                amount: subtotal,
                shippingCost: shippingCost,
                totalAmount: totalAmount,
                orderNumber: `ORD-${new Date().getFullYear()}-${Math.floor(Math.random() * 100000)}`,
                notes: cartTotal.order?.notes,
                shippingMethod: shippingMethod,
                shippingAddressId: shippingAddressId,
                showroomId: showroomId,
                createdBy: user.id,
              },
              select: {
                id: true,
                orderNumber: true,
                shippingCost: true,
                totalAmount: true,
              },
            })
          );

          await withRetry(() =>
            tx.orderItem.createMany({
              data: orderItems.map((item) => ({
                orderId: createdOrder.id,
                productId: item.id,
                quantity: item.quantity,
                price: toSafeNumber(item.FinalPrice) ?? 0, // Fresh price from DB
                notes: item.vinNotes,
                notesToInvoice: item.addVinNotesToInvoice,
                vinOrderItem: item.vinNotes,
                createdBy: user.id,
              })),
            })
          );

          return createdOrder;
        });

      if (!order) {
        logger.error(`[placeOrder] No order created for user ${user.id}`);
        return { success: false };
      }

      // Send email for confirmation with fresh data
      orderData = {
        orderId: order.orderNumber,
        customerName: `${user.firstName} ${user.lastName}`,
        customerEmail: user.email,
        customerId: user.id,
        items: orderItems.map(item => ({
          name: item.Description_Local ?? "",
          code: item.Material_Number,
          quantity: item.quantity,
          price: toSafeNumber(item.FinalPrice) ?? 0, // Fresh price
        })),
        total: subtotal, // Use calculated subtotal with fresh prices
      };

      const emailResult = await sendOrderConfirmationEmail(orderData);

      if (!emailResult.success) {
        logger.error(`[placeOrder] Email confirmation failed for order ${order.orderNumber}`);
      }

      // Update cart in Redis
      if (remainingCartItems.length === 0) {
        // If no items remain, delete the entire cart
        await redis.del(`cart-${user.id}`);
      } else {
        // Otherwise, update with remaining items
        const updatedCart: Cart = {
          ...cartTotal,
          items: remainingCartItems,
        };
        await redis.set(`cart-${user.id}`, updatedCart);
      }

    } catch (error) {
      logger.error(`[placeOrder] Error fetching fresh product data: ${error}`);
      return { success: false };
    }

    if (order && orderData) {
      revalidatePath("/");
      redirect(`/order-conf?order=${order.orderNumber}`, RedirectType.replace);
    }

    return { success: false };
}

// export async function placeOrder(shippingSelection: ShippingSelection) {

//     const user = await getCurrentDbUser();
//     if (!user) {
//       logger.error(`[placeOrder] No user authenticated`);
//       return { success: false };
//     }

//     if (!redis) {
//       logger.error("[placeOrder] Redis connection not available");
//       return { success: false };
//     }

//     // Retrieve the cart from Redis
//     const cartTotal = await redis.get<Cart>(`cart-${user.id}`);

//     if (!cartTotal) {
//       logger.error(`[placeOrder] No cart found for user ${user.id}`);
//       return { success: false };
//     }

//     if (cartTotal.items.length === 0) {
//       logger.error(`[placeOrder] No items in cart for user ${user.id}`);
//       return { success: false };
//     }

//     // Validate shipping selection and prepare order data
//     let shippingAddressId: string | null = null;
//     let shippingMethod: ShippingMethodType = 'curier';
//     let showroomId: string | null = null;

//     // Courier delivery with saved address
//     if (shippingSelection.method === 'curier' && shippingSelection.addressId) {
//       // Courier delivery with saved address
//       const userAddresses = await getUserShippingAddresses(user.id);
//       const selectedAddress = userAddresses.find(addr => addr.id === shippingSelection.addressId);

//       if (!selectedAddress) {
//         logger.error(`[placeOrder] Selected address not found: ${shippingSelection.addressId}`);
//         return { success: false };
//       }

//       shippingAddressId = selectedAddress.id;
//       shippingMethod = 'curier';
//     } else if (shippingSelection.method === 'intern') {
//       // Internal shipment - no address needed
//       shippingMethod = 'intern';
//     } else if (shippingSelection.method === 'showroom' && shippingSelection.showroomId) {
//       // Showroom pickup - validate showroom exists
//       logger.info(`[placeOrder] Validating showroom: ${shippingSelection.showroomId}`);

//       try {
//         const showroom = await prisma.showroom.findUnique({
//           where: { id: shippingSelection.showroomId, isActive: true },
//           select: { id: true, name: true, isActive: true }
//         });

//         if (!showroom) {
//           logger.error(`[placeOrder] Showroom not found or inactive: ${shippingSelection.showroomId}`);
//           return { success: false, error: 'Showroom-ul selectat nu este disponibil' };
//         }

//         logger.info(`[placeOrder] Showroom validated successfully: ${showroom.id} - ${showroom.name} (active: ${showroom.isActive})`);
//         shippingMethod = 'showroom';
//         showroomId = shippingSelection.showroomId;
//       } catch (error) {
//         logger.error(`[placeOrder] Error validating showroom: ${error}`);
//         return { success: false, error: 'Eroare la validarea showroom-ului' };
//       }
//     } else {
//       logger.error(`[placeOrder] Invalid shipping selection: ${JSON.stringify(shippingSelection)}`);
//       return { success: false };
//     }

//     // Filter out items that are not meant for order, they must have addToOrder set to true
//     const cart = { ...cartTotal, items: cartTotal.items.filter(item => item.addToOrder) };

//     const remainingCartItems  = { ...cartTotal, items: cartTotal.items.filter(item => !item.addToOrder) };

//     if(cart.items.length === 0){
//       logger.error(`[placeOrder] No items in cart for user ${user.id}`);
//       return { success: false };
//     }

//     //if the final price is 0 or null then return success false
//     if(cart.items.reduce((acc, item) => acc + (item.FinalPrice ?? 0) * item.quantity, 0) === 0){
//       logger.error(`[placeOrder] No items in cart for user ${user.id}`);
//       return { success: false };
//     }

//     // Calculate totals
//     const subtotal = cart.items
//       .filter(item => item.addToOrder)
//       .reduce((acc, item) => acc + (item.FinalPrice ?? 0) * item.quantity, 0);

//     const totalWeight = cart.items
//       .filter(item => item.addToOrder)
//       .reduce((acc, item) => acc + (item.Net_Weight ?? 0) * item.quantity, 0);

//     // Calculate shipping
//     const shippingCost = ShippingCalculator.calculateShipping({
//       subtotal,
//       totalWeight: totalWeight * 0.1,
//       shippingMethod,
//     });

//     const totalAmount = subtotal + shippingCost;

//     let order: { id: string; orderNumber: string } | null = null;

//     try {
//       logger.info(`[placeOrder] Creating order with showroomId: ${showroomId}, shippingMethod: ${shippingMethod}`);

//       order = await prisma.$transaction(async (tx) => {
//         const createdOrder = await withRetry(() =>
//           tx.order.create({
//             data: {
//               userId: user.id,
//               amount: subtotal,
//               shippingCost: shippingCost,
//               totalAmount: totalAmount,
//               orderNumber: `ORD-${new Date().getFullYear()}-${Math.floor(Math.random() * 100000)}`,
//               notes: cart.order?.notes,
//               shippingMethod: shippingMethod,
//               shippingAddressId: shippingAddressId,
//               showroomId: showroomId,
//               createdBy: user.id,
//             },
//             select: {
//               id: true,
//               orderNumber: true,
//               shippingCost: true,
//               totalAmount: true,
//             },
//           })
//         );

//         await withRetry(() =>
//           tx.orderItem.createMany({
//             data: cart.items.map((item) => ({
//               orderId: createdOrder.id,
//               productId: item.id,
//               quantity: item.quantity,
//               price: item.FinalPrice ?? 0,
//               notes: item.vinNotes,
//               notesToInvoice: item.addVinNotesToInvoice,
//               vinOrderItem: item.vinNotes,
//               createdBy: user.id,
//             })),
//           })
//         );

//         return createdOrder; // Return the order from the transaction
//       });
//     } catch (error) {
//       logger.error("Order creation transaction failed:", error);
//     }

//     if (!order) {
//       logger.error(`[placeOrder] No order created for user ${user.id}`);
//       return { success: false };
//     }

//     //send email for confirmation
//     const orderData: OrderDataEmail = {
//       orderId: order.orderNumber,
//       customerName: `${user.firstName} ${user.lastName}`,
//       customerEmail: user.email,
//       customerId: user.id,
//       items: cart.items.map(item => ({
//         name: item.Description_Local ?? "",
//         code: item.Material_Number,
//         quantity: item.quantity,
//         price: item.FinalPrice ?? 0,
//       })),
//       total: cart.items.reduce((acc, item) => acc + (item.FinalPrice ? item.FinalPrice : 0) * item.quantity, 0),
//     };

//     const emailResult = await sendOrderConfirmationEmail(orderData);

//     if (!emailResult.success) {
//       logger.error(`[placeOrder] Email confirmation failed for order ${order.orderNumber}`);
//     }

//     if (remainingCartItems.items.length === 0) {
//       // If no items remain, delete the entire cart
//       await redis.del(`cart-${user.id}`);
//     } else {
//       // Otherwise, update with remaining items
//         const updatedCart: Cart = {
//           ...cartTotal,
//           items: remainingCartItems.items,
//         };
//       await redis.set(`cart-${user.id}`, updatedCart);
//     }

//     //await redis.del(`cart-${user.id}`);
//     revalidatePath("/");
//     redirect(`/order-conf?order=${order.orderNumber}`, RedirectType.replace);
// }

// export async function place4thLevelOrder() {
//   try {
//     const user = await getCurrentDbUser();
//     if (!user) {
//       logger.error(`[4thLevelOrder] No user authenticated`);
//       return 
//     }

//     if (!redis) {
//       logger.error("[4thLevelOrder] Redis connection not available");
//       return 
//     }

//     // Retrieve the cart from Redis
//     const cart = await redis.get<Cart>(`cart-${user.id}`);

//     if (!cart) {
//       logger.error(`[4thLevelOrder] No cart found for user ${user.id}`);
//       return 
//     }

//     if (!cart.items.length) {
//       logger.error(`[4thLevelOrder] No items in cart for user ${user.id}`);
//       return
//     }

//     // Create the order
//     const order = await withRetry(() =>
//       prisma.order.create({
//         data: {
//           userId: user.id,
//           amount: cart.items.reduce((acc, item) => acc + (item.FinalPrice ? item.FinalPrice : 0) * item.quantity, 0),
//           orderNumber: `ORD-${new Date().getFullYear()}-${Math.floor(Math.random() * 100000)}`,
//           notes: cart.order?.notes,
//           createdBy: user.id,
//         },
//       })
//     );

//     // Create the order items
//     await withRetry(() =>
//       prisma.orderItem.createMany({
//         data: cart.items.map((item) => ({
//           orderId: order.id,
//           productId: item.id,
//           quantity: item.quantity,
//           price: item.FinalPrice ?? 0,
//           notes: item.vinNotes,
//           notesToInvoice: item.addVinNotesToInvoice,
//           vinOrderItem: item.vinNotes,
//           createdBy: user.id,
//         })),
//       })
//     );

//     await redis.del(`cart-${user.id}`);
//     await redis.del(`user-orders:${user.id}`);
//     revalidatePath("/");
//     redirect(`/order-conf?order=${order.orderNumber}`);
//   } catch (error) {
//     logger.error(`[4thLevelOrder] Error placing order: ${error}`);
//     return 
//   } finally {
//      logger.info(`[4thLevelOrder] Order placed successfully`);
//   }
// }