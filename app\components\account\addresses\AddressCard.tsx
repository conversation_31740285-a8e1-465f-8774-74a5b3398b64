"use client";

import { useTransition } from "react";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  MoreVertical, 
  Edit, 
  Trash2, 
  Star, 
  StarOff,
  MapPin,
  Building,
  Phone,
  CreditCard,
  Banknote
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { BillingAddress, ShippingAddress, AddressType } from "@/types/addresses";
import {
  setDefaultBillingAddress,
  setDefaultShippingAddress
} from "@/app/actions/addresses";
import { toast } from "sonner";

interface AddressCardProps {
  address: BillingAddress | ShippingAddress;
  type: AddressType;
  onEdit: (address: BillingAddress | ShippingAddress) => void;
  onDelete: (address: BillingAddress | ShippingAddress) => void;
}

export default function AddressCard({ address, type, onEdit, onDelete }: AddressCardProps) {
  const [isPending, startTransition] = useTransition();

  const handleSetDefault = () => {
    startTransition(async () => {
      const result = type === 'billing' 
        ? await setDefaultBillingAddress(address.id)
        : await setDefaultShippingAddress(address.id);

      if (result.success) {
        toast.success(
          type === 'billing' 
            ? 'Adresa de facturare implicită a fost setată'
            : 'Adresa de livrare implicită a fost setată'
        );
      } else {
        toast.error(result.error || 'A apărut o eroare');
      }
    });
  };

  const handleDelete = () => {
    onDelete(address);
  };

  const isBillingAddress = (addr: BillingAddress | ShippingAddress): addr is BillingAddress => {
    return type === 'billing';
  };

  const isShippingAddress = (addr: BillingAddress | ShippingAddress): addr is ShippingAddress => {
    return type === 'shipping';
  };

  return (
    <Card className={`relative transition-all duration-200 hover:shadow-md ${address.isDefault ? 'ring-2 ring-primary' : ''}`}>
      <CardHeader className="flex flex-row items-start justify-between space-y-0 pb-3">
        <div className="flex items-center gap-2">
          {type === 'billing' ? (
            <CreditCard className="h-5 w-5 text-muted-foreground" />
          ) : (
            <MapPin className="h-5 w-5 text-muted-foreground" />
          )}
          <h3 className="font-semibold text-lg">{address.fullName}</h3>
          {address.isDefault && (
            <Badge variant="default" className="ml-2">
              <Star className="h-3 w-3 mr-1" />
              Implicită
            </Badge>
          )}
        </div>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button 
              variant="ghost" 
              size="sm" 
              className="h-8 w-8 p-0"
              disabled={isPending}
            >
              <MoreVertical className="h-4 w-4" />
              <span className="sr-only">Opțiuni adresă</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem onClick={() => onEdit(address)}>
              <Edit className="h-4 w-4 mr-2" />
              Editează
            </DropdownMenuItem>
            {!address.isDefault && (
              <DropdownMenuItem onClick={handleSetDefault} disabled={isPending}>
                <Star className="h-4 w-4 mr-2" />
                Setează ca implicită
              </DropdownMenuItem>
            )}
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              onClick={handleDelete}
              className="text-destructive focus:text-destructive"
              disabled={isPending}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Șterge
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </CardHeader>

      <CardContent className="space-y-3">
        <div className="space-y-2">
          <div className="flex items-start gap-2">
            <MapPin className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
            <div className="text-sm">
              <p>{address.address}</p>
              <p className="text-muted-foreground">{address.city}, {address.county}</p>
            </div>
          </div>

          {isBillingAddress(address) && address.companyName && (
            <div className="flex items-center gap-2">
              <Building className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{address.companyName}</span>
            </div>
          )}

          {isBillingAddress(address) && address.cui && (
            <div className="flex items-center gap-2">
              <CreditCard className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">CUI: {address.cui}</span>
            </div>
          )}

          {isBillingAddress(address) && address.iban && (
            <div className="flex items-center gap-2">
              <Banknote className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-mono">{address.iban}</span>
            </div>
          )}

          {isBillingAddress(address) && address.bank && (
            <div className="flex items-center gap-2">
              <Building className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{address.bank}</span>
            </div>
          )}

          {isShippingAddress(address) && (
            <div className="flex items-center gap-2">
              <Phone className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{address.phoneNumber}</span>
            </div>
          )}

          {isShippingAddress(address) && address.notes && (
            <div className="text-sm text-muted-foreground">
              <p className="italic">&ldquo;{address.notes}&rdquo;</p>
            </div>
          )}
        </div>

        <div className="flex justify-between items-center pt-2 border-t">
          <span className="text-xs text-muted-foreground">
            Creată: {new Date(address.createdAt).toLocaleDateString('ro-RO')}
          </span>
          {!address.isDefault && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleSetDefault}
              disabled={isPending}
              className="h-7 px-2 text-xs"
            >
              <StarOff className="h-3 w-3 mr-1" />
              Setează implicită
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
