import { ShippingCalculationInput, ShippingMethodType } from "./addresses";

export type Cart = {
  items: CartItem[];
  order?: {
    notes?: string;
  }
};

export type CartItem = {
  id: string
  Material_Number: string
  Net_Weight: number | null
  ImageUrl: string
  Description_Local: string | null
  FinalPrice: number | null
  quantity: number 
  vinNotes: string | null
  addVinNotesToInvoice: boolean, 
  addToOrder: boolean, 
}

export class ShippingCalculator {
  private static readonly FREE_SHIPPING_THRESHOLD = 200; //200 ron
  private static readonly STANDARD_SHIPPING_RATE = 20; //20 ron
  private static readonly WEIGHT_BASED_RATE_PER_KG = 5; //5 ron per kg

  static calculateShipping(input: ShippingCalculationInput): number {
    const { subtotal, totalWeight, shippingMethod } = input;

    // Optional: showroom/intern always free regardless of weight
    if (shippingMethod === 'showroom' || shippingMethod === 'intern') {
      return 0;
    }

    if (subtotal >= this.FREE_SHIPPING_THRESHOLD) return 0;
    if (subtotal === 0) return 0;

    if (totalWeight && totalWeight > 0) {
      return this.calculateWeightBasedShipping(totalWeight, shippingMethod);
    }

    return this.STANDARD_SHIPPING_RATE;
  }

  private static calculateWeightBasedShipping(
    weight: number,
    method: ShippingMethodType // <- now optional
  ): number {
    const baseRate = Math.ceil(weight) * this.WEIGHT_BASED_RATE_PER_KG;

    switch (method) {
      case 'curier':
        return baseRate;
      case 'intern':    // in-house delivery => free
        return 0;
      case 'showroom':  // pickup => free
        return 0;
      default:
        return baseRate; // when method is undefined or unknown
    }
  }
}