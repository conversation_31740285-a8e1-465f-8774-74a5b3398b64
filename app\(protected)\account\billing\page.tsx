"server-only"

import { getCurrentDbUser } from "@/lib/auth";
import { getUserBillingAddresses } from "@/app/getData/addresses";
import { redirect } from "next/navigation";
import { logger } from "@/lib/logger";
import AddressList from "@/app/components/account/addresses/AddressList";
//import AddressListError from "@/app/components/account/addresses/AddressListError";

export default async function BillingPage() {
  try {
    // Get current authenticated user
    const user = await getCurrentDbUser();

    if (!user) {
      logger.warn('[BillingPage] No authenticated user found');
      redirect("/sign-in");
    }

    // Fetch billing addresses
    const addresses = await getUserBillingAddresses(user.id);

    return (
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        <AddressList
          addresses={addresses}
          type="billing"
          title="Adrese de facturare"
          description="Gestionați adresele de facturare pentru comenzile dumneavoastră"
          emptyStateMessage="Nu aveți încă adrese de facturare salvate. Adăugați prima adresă pentru a facilita procesul de comandă."
        />
      </div>
    );

  } catch (error) {
    logger.error('[BillingPage] Error loading billing page:', error);
    redirect("/account/settings");
  }
}