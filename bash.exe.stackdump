Stack trace:
Frame         Function      Args
0007FFFFA160  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9060) msys-2.0.dll+0x1FE8E
0007FFFFA160  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA438) msys-2.0.dll+0x67F9
0007FFFFA160  000210046832 (000210286019, 0007FFFFA018, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA160  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA160  000210068E24 (0007FFFFA170, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA440  00021006A225 (0007FFFFA170, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFEB8CA0000 ntdll.dll
7FFEB7AD0000 KERNEL32.DLL
7FFEB6310000 KERNELBASE.dll
000074D80000 sysfer.dll
7FFEB78E0000 USER32.dll
7FFEB6000000 win32u.dll
7FFEB78B0000 GDI32.dll
7FFEB68F0000 gdi32full.dll
000210040000 msys-2.0.dll
7FFEB5DD0000 msvcp_win.dll
7FFEB6710000 ucrtbase.dll
7FFEB76C0000 advapi32.dll
7FFEB7800000 msvcrt.dll
7FFEB6A30000 sechost.dll
7FFEB87A0000 RPCRT4.dll
7FFEB52C0000 CRYPTBASE.DLL
7FFEB6030000 bcryptPrimitives.dll
7FFEB6CE0000 IMM32.DLL
