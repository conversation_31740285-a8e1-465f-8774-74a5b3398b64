// Address types for client components (no Prisma types)

export interface BillingAddress {
  id: string;
  fullName: string;
  companyName?: string;
  address: string;
  city: string;
  county: string;
  cui?: string; // Romanian tax ID
  bank?: string;
  iban?: string;
  isDefault: boolean;
  createdAt: string; // ISO string
  updatedAt: string; // ISO string
}

export interface ShippingAddress {
  id: string;
  fullName: string;
  address: string;
  city: string;
  county: string;
  phoneNumber: string;
  notes?: string;
  isDefault: boolean;
  createdAt: string; // ISO string
  updatedAt: string; // ISO string
}

// Form input types
export interface BillingAddressInput {
  fullName: string;
  companyName?: string;
  address: string;
  city: string;
  county: string;
  cui?: string;
  bank?: string;
  iban?: string;
}

export interface ShippingAddressInput {
  fullName: string;
  address: string;
  city: string;
  county: string;
  phoneNumber: string;
  notes?: string;
}

// API response types
export interface AddressActionResult {
  success: boolean;
  error?: string;
  data?: BillingAddress | ShippingAddress;
}

// Address type discriminator
export type AddressType = 'billing' | 'shipping';

// Combined address type for generic operations
export type Address = BillingAddress | ShippingAddress;

// Form state types
export interface AddressFormState {
  isOpen: boolean;
  mode: 'create' | 'edit';
  address?: BillingAddress | ShippingAddress;
}

// Delete confirmation state
export interface DeleteAddressState {
  isOpen: boolean;
  address?: BillingAddress | ShippingAddress;
  type: AddressType;
}

// Shipping method types (matching Prisma enum)
export type ShippingMethodType = 'curier' | 'intern' | 'showroom';

export interface ShippingSelection {
  method: ShippingMethodType;
  addressId?: string; // for curier method with saved addresses
  showroomId?: string; // for showroom method - now using showroom ID
  // For intern method, no additional data needed
}

// Showroom type (from Prisma)
export interface ShowroomData {
  id: string;
  code: string;
  name: string;
  address1: string;
  address2: string | null;
  city: string;
  county: string;
  postalCode: string | null;
  phone: string;
  email: string | null;
  program: string | null;
  isActive: boolean;
  sortOrder: number | null;
}

// Props for AddressSelector component
export interface AddressSelectorProps {
  addresses: ShippingAddress[];
  showrooms?: ShowroomData[];
  value?: ShippingSelection;
  onValueChange: (selection: ShippingSelection | undefined) => void;
  className?: string;
}

export interface ShippingCalculationInput {
  subtotal: number;
  totalWeight?: number;
  shippingMethod: ShippingMethodType;
}

export interface ShippingRate {
  method: string;
  cost: number;
  estimatedDays?: number;
  description?: string;
}
